# 动态提示词系统使用指南

## 🚀 概述

动态提示词系统完全基于文件内容，无硬编码，支持灵活配置和扩展。

## 📁 文件结构

```
app/testCasePromptAndFiles/
├── prompt_config.yml           # 配置文件（可选）
├── 枚举值提示词.txt            # 枚举值测试提示词
├── 必填参数提示词.txt          # 必填参数测试提示词
├── 接口生成提示词.txt          # 接口生成提示词
├── 标准化用例参考格式.yml      # 测试用例参考格式
└── 标准接口参考格式.yml        # 接口文件参考格式
```

## 🔄 配置方式

### 方式1：配置文件驱动（推荐）

在 `prompt_config.yml` 中定义提示词类型：

```yaml
prompt_types:
  枚举值集测:
    prompt_file: "枚举值提示词.txt"
    reference_file: "标准化用例参考格式.yml"
    description: "自动提取枚举参数并生成测试用例"
    
  必填参数集测:
    prompt_file: "必填参数提示词.txt"
    reference_file: "标准化用例参考格式.yml"
    description: "生成必填参数缺失场景的测试用例"
```

### 方式2：代码配置（兜底）

如果配置文件不存在，系统会使用硬编码的默认配置。

## 🛠️ 使用方法

### 1. 获取特定类型的提示词

```python
from app.business_classes.test_case_prompt_service import TestCasePromptService

service = TestCasePromptService()

# 获取枚举值集测提示词
result = service.获取枚举值集测提示词和参考文件()
full_prompt = result['data']['full_prompt']
```

### 2. 通用提示词获取

```python
# 支持任意配置的提示词类型
result = service.获取通用提示词("枚举值集测")
full_prompt = result['data']['full_prompt']
```

### 3. 动态添加新类型

```python
# 添加新的提示词类型
result = service.添加新的提示词类型(
    类型名称="边界值集测",
    提示词文件="边界值提示词.txt",
    参考文件="标准化用例参考格式.yml"
)
```

## ✨ 核心特性

### 1. 完全动态化
- ✅ 无硬编码的提示词内容
- ✅ 基于文件内容动态拼接
- ✅ 智能识别文件引用并替换

### 2. 配置驱动
- ✅ 支持YAML配置文件
- ✅ 热重载配置
- ✅ 兜底默认配置

### 3. 智能拼接
- ✅ 自动检测提示词中的引用
- ✅ 动态替换参考文件内容
- ✅ 保持格式完整性

### 4. 扩展性强
- ✅ 支持动态添加新类型
- ✅ 无需修改代码
- ✅ 配置即生效

## 🔧 添加新的提示词类型

### 步骤1：创建文件

1. 在 `testCasePromptAndFiles` 目录下创建提示词文件（.txt）
2. 创建或复用参考文件（.yml）

### 步骤2：更新配置

在 `prompt_config.yml` 中添加新类型：

```yaml
prompt_types:
  新类型名称:
    prompt_file: "新提示词.txt"
    reference_file: "新参考格式.yml"
    description: "功能描述"
```

### 步骤3：重新加载

```python
service = TestCasePromptService()
result = service.重新加载配置()
```

## 📋 API 方法列表

| 方法名 | 功能 | 参数 |
|--------|------|------|
| `获取枚举值集测提示词和参考文件()` | 获取枚举值测试提示词 | 无 |
| `获取必填参数集测提示词和参考文件()` | 获取必填参数测试提示词 | 无 |
| `获取接口生成提示词和参考文件()` | 获取接口生成提示词 | 无 |
| `获取通用提示词(类型)` | 通用提示词获取 | 提示词类型 |
| `获取所有提示词类型()` | 获取支持的类型列表 | 无 |
| `添加新的提示词类型()` | 动态添加新类型 | 类型名称、文件名 |
| `重新加载配置()` | 重新加载配置文件 | 无 |
| `获取提示词文件列表()` | 获取文件列表 | 无 |
| `获取单个文件内容(文件名)` | 获取指定文件内容 | 文件名 |

## 🎯 最佳实践

1. **使用配置文件**：优先使用 `prompt_config.yml` 管理配置
2. **文件命名规范**：提示词文件使用 `.txt`，参考文件使用 `.yml`
3. **内容引用**：在提示词中使用 "参考用例格式.yml" 或 "参考格式" 来引用参考文件
4. **编码格式**：所有文件使用 UTF-8 编码
5. **测试验证**：添加新类型后及时测试验证

## 🔍 故障排除

### 问题1：配置文件不生效
- 检查文件路径和格式
- 调用 `重新加载配置()` 方法
- 查看日志输出

### 问题2：提示词内容不完整
- 确认文件存在且可读
- 检查文件编码为 UTF-8
- 验证文件内容格式

### 问题3：引用替换失败
- 检查提示词中的引用格式
- 确认参考文件存在
- 查看动态拼接逻辑

## 📈 架构优势

1. **维护性**：配置与代码分离，易于维护
2. **扩展性**：支持无限扩展新的提示词类型
3. **灵活性**：支持运行时动态配置
4. **可靠性**：多层兜底机制，确保系统稳定
5. **易用性**：简单的API接口，易于集成
