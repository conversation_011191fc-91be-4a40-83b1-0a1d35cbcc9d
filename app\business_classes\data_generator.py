"""
数据生成业务类
=============

只需要定义这个类，系统自动生成MCP工具！
"""
import random
from typing import Dict, Any
from datetime import datetime, timedelta
from app.business_classes import BusinessBase


class DataGenerator(BusinessBase):
    """数据生成业务类"""
    
    class Meta:
        name = "数据生成"
        icon = "🎲"
        description = "生成各种测试数据，包括手机号、身份证、银行卡、邮箱、用户档案等"
        category = "utility"
        ai_prompt_hint = "当用户需要生成测试数据时，如手机号、身份证号、银行卡号、邮箱地址、完整用户档案等，请使用此类的方法"
    
    def 生成手机号(self, 数量: int = 1, 运营商: str = "随机") -> Dict[str, Any]:
        """
        生成随机手机号码
        
        :param 数量: 生成手机号的数量，默认1个
        :param 运营商: 运营商类型 (移动/联通/电信/随机)，默认随机
        :return: 生成的手机号列表
        """
        try:
            # 不同运营商的号段
            mobile_prefixes = {
                "移动": ["134", "135", "136", "137", "138", "139", "147", "150", "151", "152", "157", "158", "159", "178", "182", "183", "184", "187", "188", "198"],
                "联通": ["130", "131", "132", "145", "155", "156", "166", "171", "175", "176", "185", "186", "196"],
                "电信": ["133", "149", "153", "173", "177", "180", "181", "189", "191", "193", "199"]
            }
            
            # 选择号段
            if 运营商 == "随机":
                all_prefixes = []
                for prefixes in mobile_prefixes.values():
                    all_prefixes.extend(prefixes)
                prefixes = all_prefixes
            else:
                prefixes = mobile_prefixes.get(运营商, mobile_prefixes["移动"])
            
            # 生成手机号
            phone_numbers = []
            for _ in range(数量):
                prefix = random.choice(prefixes)
                suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
                phone_number = prefix + suffix
                phone_numbers.append(phone_number)
            
            return {
                "status": "success",
                "data": {
                    "手机号列表": phone_numbers,
                    "数量": len(phone_numbers),
                    "运营商": 运营商
                },
                "message": f"成功生成{len(phone_numbers)}个{运营商}手机号"
            }
            
        except Exception as e:
            self.logger.error(f"生成手机号失败: {str(e)}")
            return {
                "status": "error",
                "message": f"生成手机号失败: {str(e)}"
            }
    
    def 生成身份证号(self, 数量: int = 1, 地区: str = "北京") -> Dict[str, Any]:
        """
        生成随机身份证号码
        
        :param 数量: 生成身份证号的数量，默认1个
        :param 地区: 地区名称，默认北京
        :return: 生成的身份证号列表
        """
        try:
            # 地区代码映射
            area_codes = {
                "北京": "110101",
                "上海": "310101", 
                "广州": "440101",
                "深圳": "440301",
                "杭州": "330101",
                "南京": "320101",
                "成都": "510101",
                "重庆": "500101"
            }
            
            area_code = area_codes.get(地区, "110101")
            
            id_numbers = []
            for _ in range(数量):
                # 生成出生日期 (1970-2000年)
                start_date = datetime(1970, 1, 1)
                end_date = datetime(2000, 12, 31)
                random_date = start_date + timedelta(
                    days=random.randint(0, (end_date - start_date).days)
                )
                birth_date = random_date.strftime("%Y%m%d")
                
                # 生成顺序码 (奇数为男，偶数为女)
                sequence = str(random.randint(100, 999))
                
                # 前17位
                id_17 = area_code + birth_date + sequence
                
                # 计算校验码
                weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
                check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
                
                sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
                check_code = check_codes[sum_val % 11]
                
                id_number = id_17 + check_code
                id_numbers.append(id_number)
            
            return {
                "status": "success",
                "data": {
                    "身份证号列表": id_numbers,
                    "数量": len(id_numbers),
                    "地区": 地区
                },
                "message": f"成功生成{len(id_numbers)}个{地区}身份证号"
            }
            
        except Exception as e:
            self.logger.error(f"生成身份证号失败: {str(e)}")
            return {
                "status": "error",
                "message": f"生成身份证号失败: {str(e)}"
            }
    
    def 生成完整用户档案(self, 数量: int = 1, 地区: str = "北京") -> Dict[str, Any]:
        """
        生成完整的用户档案信息
        
        :param 数量: 生成档案的数量，默认1个
        :param 地区: 地区名称，默认北京
        :return: 完整用户档案列表
        """
        try:
            profiles = []
            
            for _ in range(数量):
                # 生成各种信息
                phone_result = self.生成手机号(1, "随机")
                id_result = self.生成身份证号(1, 地区)
                
                # 生成姓名
                surnames = ["王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴"]
                given_names = ["伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋"]
                name = random.choice(surnames) + random.choice(given_names)
                
                profile = {
                    "姓名": name,
                    "手机号": phone_result["data"]["手机号列表"][0] if phone_result["status"] == "success" else "",
                    "身份证号": id_result["data"]["身份证号列表"][0] if id_result["status"] == "success" else "",
                    "地区": 地区
                }
                profiles.append(profile)
            
            return {
                "status": "success",
                "data": {
                    "用户档案列表": profiles,
                    "数量": len(profiles),
                    "地区": 地区
                },
                "message": f"成功生成{len(profiles)}个完整用户档案"
            }
            
        except Exception as e:
            self.logger.error(f"生成完整用户档案失败: {str(e)}")
            return {
                "status": "error",
                "message": f"生成完整用户档案失败: {str(e)}"
            }
