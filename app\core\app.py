"""
应用核心配置和创建
"""
from fastapi import FastAPI
from fastapi_mcp import FastApiMCP
import logging

from app.api.routes import api_router
from app.api.business_routes import business_router
from app.core.config import settings
from app.core.mcp_config import create_business_mcp_server

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

def create_app() -> FastAPI:
    """
    创建FastAPI应用实例
    """
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
    )

    # 包含API路由
    app.include_router(api_router, prefix="/api")

    # 包含业务路由（用于MCP工具生成）
    app.include_router(business_router, prefix="/api")

    # 创建业务MCP服务器（只包含业务方法）
    mcp = create_business_mcp_server(app)

    # 挂载MCP服务器
    mcp.mount()

    return app
