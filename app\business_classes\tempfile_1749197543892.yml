- config:
    name: 创建有账号证书申请任务
    variables:
      bizType: TCLOUD
      operatorType: APPLY
      projectId: "**********"
      notifyUrl: "http://libaohui.com.cn/callback/ding"
      redirectUrl: "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai"
      phone: "19888644846"
      certNum: 1
      certName: "esigntest苍诚经营的个体工商户"
      licenseNumber: "91000000BL0X3BDKXJ"
      userType: 2

# 正向用例：bizType - TIANYIN
- test:
    name: 创建有账号证书申请任务-bizType-TIANYIN
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "TIANYIN",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：bizType - TIANYIN_OFFLINE
- test:
    name: 创建有账号证书申请任务-bizType-TIANYIN_OFFLINE
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "TIANYIN_OFFLINE",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：bizType - ESHIELD
- test:
    name: 创建有账号证书申请任务-bizType-ESHIELD
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "ESHIELD",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：bizType - TCLOUD
- test:
    name: 创建有账号证书申请任务-bizType-TCLOUD
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：bizType - PUBCLOUD
- test:
    name: 创建有账号证书申请任务-bizType-PUBCLOUD
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "PUBCLOUD",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 反向用例：bizType - 非法值（超出范围）
- test:
    name: 创建有账号证书申请任务-bizType-非法值
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "INVALID_BIZ_TYPE",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 400]
      - eq: ["content.message", "参数错误: bizType 不合法"]

# 正向用例：operatorType - APPLY
- test:
    name: 创建有账号证书申请任务-operatorType-APPLY
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "APPLY",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：operatorType - DELAY
- test:
    name: 创建有账号证书申请任务-operatorType-DELAY
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "DELAY",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：operatorType - UPDATE
- test:
    name: 创建有账号证书申请任务-operatorType-UPDATE
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "UPDATE",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 反向用例：operatorType - 非法值（超出范围）
- test:
    name: 创建有账号证书申请任务-operatorType-非法值
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "INVALID_OPERATOR_TYPE",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 400]
      - eq: ["content.message", "参数错误: operatorType 不合法"]

# 正向用例：agentType - 1
- test:
    name: 创建有账号证书申请任务-agentType-1
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {
            "agentType": 1
          },
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：agentType - 2
- test:
    name: 创建有账号证书申请任务-agentType-2
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {
            "agentType": 2
          },
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 反向用例：agentType - 非法值（超出范围）
- test:
    name: 创建有账号证书申请任务-agentType-非法值
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {
            "agentType": 3
          },
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 400]
      - eq: ["content.message", "参数错误: agentType 不合法"]

# 正向用例：algorithm - RSA
- test:
    name: 创建有账号证书申请任务-algorithm-RSA
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum",
            "algorithm": "RSA"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：algorithm - SM2
- test:
    name: 创建有账号证书申请任务-algorithm-SM2
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum",
            "algorithm": "SM2"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 反向用例：algorithm - 非法值（超出范围）
- test:
    name: 创建有账号证书申请任务-algorithm-非法值
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum",
            "algorithm": "INVALID_ALGORITHM"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 400]
      - eq: ["content.message", "参数错误: algorithm 不合法"]

# 正向用例：certType - SINGLE
- test:
    name: 创建有账号证书申请任务-certType-SINGLE
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum",
            "certType": "SINGLE"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：certType - DOUBLE
- test:
    name: 创建有账号证书申请任务-certType-DOUBLE
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum",
            "certType": "DOUBLE"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 反向用例：certType - 非法值（超出范围）
- test:
    name: 创建有账号证书申请任务-certType-非法值
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum",
            "certType": "INVALID_CERT_TYPE"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 400]
      - eq: ["content.message", "参数错误: certType 不合法"]

# 正向用例：userType - 1 (个人)
- test:
    name: 创建有账号证书申请任务-userType-1
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": 1
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：userType - 2 (企业)
- test:
    name: 创建有账号证书申请任务-userType-2
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": 2
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 反向用例：userType - 非法值（超出范围）
- test:
    name: 创建有账号证书申请任务-userType-非法值
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": 3
          }
        }
    validate:
      - eq: ["content.code", 400]
      - eq: ["content.message", "参数错误: userType 不合法"]
