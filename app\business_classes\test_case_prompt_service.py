"""
集测提示词和参考文件服务
======================

提供动态的提示词拼接和参考文件内容，供Trae端使用
Trae端负责swagger解析和yml文件生成
"""
import os
import yaml
from typing import Dict, Any

from app.business_classes import BusinessBase


class TestCasePromptService(BusinessBase):
    """
    集测提示词和参考文件服务

    功能：
    1. 动态读取提示词文件
    2. 提供参考文件内容
    3. 拼接完整的提示词
    4. 支持多种测试场景

    架构说明：
    - 服务端：只负责提示词拼接和参考文件提供
    - Trae端：负责swagger解析和yml文件写入
    """

    class Meta:
        name = "集测提示词服务"
        icon = "📝"
        description = "提供动态的测试用例生成提示词，支持枚举值、必填参数、接口生成等多种场景"
        category = "testing"
        ai_prompt_hint = "当用户需要生成测试用例提示词时，如枚举值集测、必填参数集测、接口生成等，请使用此类的方法"

    def __init__(self):
        super().__init__()
        self.prompt_dir = "app/testCasePromptAndFiles"
        self.config_file = os.path.join(self.prompt_dir, "prompt_config.yml")

        # 🔄 动态文件配对规则 - 优先从配置文件读取，兜底使用硬编码
        self.file_pairs = self._load_config()
        self._ensure_prompt_dir_exists()
    
    def _load_config(self) -> Dict[str, Dict[str, str]]:
        """
        🔄 加载配置文件 - 优先从YAML配置文件读取，兜底使用硬编码

        :return: 文件配对配置
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)

                if config_data and 'prompt_types' in config_data:
                    # 从配置文件转换为内部格式
                    file_pairs = {}
                    for prompt_type, config in config_data['prompt_types'].items():
                        file_pairs[prompt_type] = {
                            "prompt_file": config["prompt_file"],
                            "reference_file": config["reference_file"]
                        }

                    self.logger.info(f"成功从配置文件加载 {len(file_pairs)} 个提示词类型")
                    return file_pairs
                else:
                    self.logger.warning("配置文件格式不正确，使用默认配置")
            else:
                self.logger.info("配置文件不存在，使用默认配置")

        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}，使用默认配置")

        # 兜底：使用硬编码的默认配置
        return {
            "枚举值集测": {
                "prompt_file": "枚举值提示词.txt",
                "reference_file": "标准化用例参考格式.yml"
            },
            "必填参数集测": {
                "prompt_file": "必填参数提示词.txt",
                "reference_file": "标准化用例参考格式.yml"
            },
            "接口生成": {
                "prompt_file": "接口生成提示词.txt",
                "reference_file": "标准接口参考格式.yml"
            }
        }

    def _ensure_prompt_dir_exists(self):
        """确保提示词目录存在"""
        if not os.path.exists(self.prompt_dir):
            self.logger.warning(f"提示词目录不存在: {self.prompt_dir}")
    
    def _read_file_content(self, filename: str) -> str:
        """读取文件内容"""
        try:
            file_path = os.path.join(self.prompt_dir, filename)
            if not os.path.exists(file_path):
                self.logger.error(f"文件不存在: {file_path}")
                return ""

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                self.logger.info(f"成功读取文件: {filename}")
                return content
        except Exception as e:
            self.logger.error(f"读取文件失败 {filename}: {str(e)}")
            return ""

    def _build_dynamic_prompt(self, prompt_type: str) -> str:
        """
        🚀 动态构建提示词 - 完全基于文件内容，无硬编码

        :param prompt_type: 提示词类型（如：枚举值集测、必填参数集测、接口生成）
        :return: 完整的提示词
        """
        try:
            if prompt_type not in self.file_pairs:
                self.logger.error(f"不支持的提示词类型: {prompt_type}")
                return ""

            pair_config = self.file_pairs[prompt_type]

            # 读取提示词文件
            prompt_content = self._read_file_content(pair_config["prompt_file"])
            if not prompt_content:
                self.logger.error(f"提示词文件读取失败: {pair_config['prompt_file']}")
                return ""

            # 读取参考文件
            reference_content = self._read_file_content(pair_config["reference_file"])
            if not reference_content:
                self.logger.error(f"参考文件读取失败: {pair_config['reference_file']}")
                return ""

            # 🔄 动态拼接 - 检查提示词中是否已包含参考文件引用
            if "参考用例格式.yml" in prompt_content or "参考格式" in prompt_content:
                # 提示词文件中已经包含了对参考文件的引用，直接替换
                full_prompt = prompt_content.replace("参考用例格式.yml", f"```yaml\n{reference_content}\n```")
                full_prompt = full_prompt.replace("参考格式", f"参考格式：\n```yaml\n{reference_content}\n```")
            else:
                # 如果没有引用，则按标准格式拼接
                full_prompt = f"""{prompt_content}

参考格式：
```yaml
{reference_content}
```

请严格按照上述要求和参考格式生成相应内容。"""

            self.logger.info(f"成功构建 {prompt_type} 提示词")
            return full_prompt

        except Exception as e:
            self.logger.error(f"构建 {prompt_type} 提示词失败: {str(e)}")
            return ""
    
    def 获取枚举值集测提示词和参考文件(self) -> Dict[str, Any]:
        """
        🚀 获取枚举值集测的完整提示词和参考文件

        供Trae端使用，包含：
        1. 枚举值提取和用例生成的提示词
        2. 标准化用例参考格式
        3. 使用说明

        :return: 完整的提示词和参考文件内容
        """
        try:
            prompt_type = "枚举值集测"
            pair_config = self.file_pairs[prompt_type]

            # 🔄 动态读取文件内容
            enum_prompt = self._read_file_content(pair_config["prompt_file"])
            case_template = self._read_file_content(pair_config["reference_file"])

            # 🚀 使用动态构建方法
            full_prompt = self._build_dynamic_prompt(prompt_type)

            return {
                "status": "success",
                "message": "枚举值集测提示词获取成功",
                "data": {
                    "prompt_type": prompt_type,
                    "full_prompt": full_prompt,
                    "components": {
                        "enum_prompt": enum_prompt,
                        "case_template": case_template
                    },
                    "file_mapping": {
                        "prompt_file": pair_config["prompt_file"],
                        "reference_file": pair_config["reference_file"]
                    },
                    "usage_guide": {
                        "description": "在Trae中使用此提示词进行枚举值集测用例生成",
                        "steps": [
                            "1. 复制full_prompt作为系统提示词",
                            "2. 提供swagger接口文档",
                            "3. 提供标准化入参",
                            "4. AI会自动生成HttpRunner格式的yml测试用例"
                        ],
                        "architecture": {
                            "server_role": "动态读取提示词文件并拼接",
                            "trae_role": "解析swagger文档，生成并写入yml文件"
                        }
                    }
                }
            }

        except Exception as e:
            self.logger.error(f"获取枚举值集测提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取失败: {str(e)}",
                "troubleshooting": [
                    "检查提示词文件是否存在",
                    "确认文件编码为UTF-8",
                    "查看详细错误日志"
                ]
            }
    
    def 获取必填参数集测提示词和参考文件(self) -> Dict[str, Any]:
        """
        🚀 获取必填参数集测的完整提示词和参考文件

        :return: 完整的提示词和参考文件内容
        """
        try:
            prompt_type = "必填参数集测"
            pair_config = self.file_pairs[prompt_type]

            # 🔄 动态读取文件内容
            required_prompt = self._read_file_content(pair_config["prompt_file"])
            case_template = self._read_file_content(pair_config["reference_file"])

            # 🚀 使用动态构建方法
            full_prompt = self._build_dynamic_prompt(prompt_type)

            return {
                "status": "success",
                "message": "必填参数集测提示词获取成功",
                "data": {
                    "prompt_type": prompt_type,
                    "full_prompt": full_prompt,
                    "components": {
                        "required_prompt": required_prompt,
                        "case_template": case_template
                    },
                    "file_mapping": {
                        "prompt_file": pair_config["prompt_file"],
                        "reference_file": pair_config["reference_file"]
                    },
                    "usage_guide": {
                        "description": "在Trae中使用此提示词进行必填参数集测用例生成",
                        "focus": "专注于必填参数缺失场景的测试用例生成"
                    }
                }
            }

        except Exception as e:
            self.logger.error(f"获取必填参数集测提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取失败: {str(e)}"
            }
    
    def 获取接口生成提示词和参考文件(self) -> Dict[str, Any]:
        """
        🚀 获取接口生成的完整提示词和参考文件

        :return: 完整的提示词和参考文件内容
        """
        try:
            prompt_type = "接口生成"
            pair_config = self.file_pairs[prompt_type]

            # 🔄 动态读取文件内容
            api_prompt = self._read_file_content(pair_config["prompt_file"])
            api_template = self._read_file_content(pair_config["reference_file"])

            # 🚀 使用动态构建方法
            full_prompt = self._build_dynamic_prompt(prompt_type)

            return {
                "status": "success",
                "message": "接口生成提示词获取成功",
                "data": {
                    "prompt_type": prompt_type,
                    "full_prompt": full_prompt,
                    "components": {
                        "api_prompt": api_prompt,
                        "api_template": api_template
                    },
                    "file_mapping": {
                        "prompt_file": pair_config["prompt_file"],
                        "reference_file": pair_config["reference_file"]
                    },
                    "usage_guide": {
                        "description": "在Trae中使用此提示词进行接口文件生成",
                        "focus": "根据swagger文档自动生成标准接口文件"
                    }
                }
            }

        except Exception as e:
            self.logger.error(f"获取接口生成提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取失败: {str(e)}"
            }
    

    
    def 获取所有提示词类型(self) -> Dict[str, Any]:
        """
        🔄 获取所有支持的提示词类型 - 基于动态配置

        :return: 支持的提示词类型列表
        """
        try:
            # 🚀 基于动态配置生成支持的类型列表
            supported_types = []

            # 类型描述映射
            type_descriptions = {
                "枚举值集测": "自动提取枚举参数并生成测试用例",
                "必填参数集测": "生成必填参数缺失场景的测试用例",
                "接口生成": "根据swagger文档生成标准接口文件"
            }

            # 方法名映射
            method_mapping = {
                "枚举值集测": "获取枚举值集测提示词和参考文件",
                "必填参数集测": "获取必填参数集测提示词和参考文件",
                "接口生成": "获取接口生成提示词和参考文件"
            }

            for prompt_type, config in self.file_pairs.items():
                supported_types.append({
                    "type": prompt_type,
                    "method": method_mapping.get(prompt_type, f"获取{prompt_type}提示词和参考文件"),
                    "description": type_descriptions.get(prompt_type, f"{prompt_type}相关功能"),
                    "files": [config["prompt_file"], config["reference_file"]],
                    "file_mapping": config
                })

            return {
                "status": "success",
                "data": {
                    "supported_types": supported_types,
                    "total_types": len(supported_types),
                    "architecture": {
                        "server_responsibility": [
                            "动态读取提示词文件",
                            "智能拼接完整的提示词",
                            "提供参考文件内容",
                            "不进行swagger解析"
                        ],
                        "trae_responsibility": [
                            "解析swagger文档",
                            "生成yml测试用例",
                            "写入文件到本地",
                            "管理测试用例文件"
                        ]
                    },
                    "usage_flow": [
                        "1. 选择需要的提示词类型",
                        "2. 调用对应的方法获取完整提示词",
                        "3. 在Trae中使用full_prompt作为系统提示词",
                        "4. 提供swagger文档和相关参数",
                        "5. AI自动生成对应的测试用例或接口文件",
                        "6. Trae负责将生成的内容写入yml文件"
                    ],
                    "dynamic_features": [
                        "✅ 完全基于文件内容，无硬编码",
                        "✅ 支持动态添加新的提示词类型",
                        "✅ 智能识别文件配对关系",
                        "✅ 自动处理提示词拼接逻辑"
                    ]
                }
            }

        except Exception as e:
            self.logger.error(f"获取提示词类型失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取提示词类型失败: {str(e)}"
            }
    
    def 获取提示词文件列表(self) -> Dict[str, Any]:
        """
        获取提示词目录下的所有文件列表
        
        :return: 文件列表和状态
        """
        try:
            if not os.path.exists(self.prompt_dir):
                return {
                    "status": "error",
                    "message": f"提示词目录不存在: {self.prompt_dir}"
                }
            
            files = []
            for filename in os.listdir(self.prompt_dir):
                file_path = os.path.join(self.prompt_dir, filename)
                if os.path.isfile(file_path):
                    file_info = {
                        "filename": filename,
                        "size": os.path.getsize(file_path),
                        "type": "提示词" if filename.endswith('.txt') else "参考文件" if filename.endswith('.yml') else "其他"
                    }
                    files.append(file_info)
            
            return {
                "status": "success",
                "data": {
                    "directory": self.prompt_dir,
                    "total_files": len(files),
                    "files": files
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取文件列表失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取文件列表失败: {str(e)}"
            }
    
    def 获取单个文件内容(self, 文件名: str) -> Dict[str, Any]:
        """
        获取指定文件的内容

        :param 文件名: 要读取的文件名
        :return: 文件内容
        """
        try:
            content = self._read_file_content(文件名)
            if not content:
                return {
                    "status": "error",
                    "message": f"文件不存在或内容为空: {文件名}"
                }

            return {
                "status": "success",
                "data": {
                    "filename": 文件名,
                    "content": content,
                    "length": len(content),
                    "type": "提示词" if 文件名.endswith('.txt') else "参考文件" if 文件名.endswith('.yml') else "其他"
                }
            }

        except Exception as e:
            self.logger.error(f"获取文件内容失败 {文件名}: {str(e)}")
            return {
                "status": "error",
                "message": f"获取文件内容失败: {str(e)}"
            }

    def 添加新的提示词类型(self, 类型名称: str, 提示词文件: str, 参考文件: str) -> Dict[str, Any]:
        """
        🚀 动态添加新的提示词类型

        :param 类型名称: 新的提示词类型名称
        :param 提示词文件: 提示词文件名
        :param 参考文件: 参考文件名
        :return: 添加结果
        """
        try:
            # 检查文件是否存在
            prompt_path = os.path.join(self.prompt_dir, 提示词文件)
            reference_path = os.path.join(self.prompt_dir, 参考文件)

            if not os.path.exists(prompt_path):
                return {
                    "status": "error",
                    "message": f"提示词文件不存在: {提示词文件}"
                }

            if not os.path.exists(reference_path):
                return {
                    "status": "error",
                    "message": f"参考文件不存在: {参考文件}"
                }

            # 添加到配置中
            self.file_pairs[类型名称] = {
                "prompt_file": 提示词文件,
                "reference_file": 参考文件
            }

            self.logger.info(f"成功添加新的提示词类型: {类型名称}")

            return {
                "status": "success",
                "message": f"成功添加提示词类型: {类型名称}",
                "data": {
                    "type_name": 类型名称,
                    "prompt_file": 提示词文件,
                    "reference_file": 参考文件,
                    "total_types": len(self.file_pairs)
                }
            }

        except Exception as e:
            self.logger.error(f"添加提示词类型失败: {str(e)}")
            return {
                "status": "error",
                "message": f"添加失败: {str(e)}"
            }

    def 获取通用提示词(self, 提示词类型: str) -> Dict[str, Any]:
        """
        🚀 通用的提示词获取方法 - 支持任意类型

        :param 提示词类型: 提示词类型名称
        :return: 完整的提示词和参考文件内容
        """
        try:
            if 提示词类型 not in self.file_pairs:
                return {
                    "status": "error",
                    "message": f"不支持的提示词类型: {提示词类型}",
                    "available_types": list(self.file_pairs.keys())
                }

            pair_config = self.file_pairs[提示词类型]

            # 动态读取文件内容
            prompt_content = self._read_file_content(pair_config["prompt_file"])
            reference_content = self._read_file_content(pair_config["reference_file"])

            # 使用动态构建方法
            full_prompt = self._build_dynamic_prompt(提示词类型)

            return {
                "status": "success",
                "message": f"{提示词类型}提示词获取成功",
                "data": {
                    "prompt_type": 提示词类型,
                    "full_prompt": full_prompt,
                    "components": {
                        "prompt_content": prompt_content,
                        "reference_content": reference_content
                    },
                    "file_mapping": pair_config,
                    "usage_guide": {
                        "description": f"在Trae中使用此提示词进行{提示词类型}",
                        "architecture": {
                            "server_role": "动态读取提示词文件并拼接",
                            "trae_role": "解析文档，生成并写入文件"
                        }
                    }
                }
            }

        except Exception as e:
            self.logger.error(f"获取{提示词类型}提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取失败: {str(e)}"
            }

    def 重新加载配置(self) -> Dict[str, Any]:
        """
        🔄 重新加载配置文件

        :return: 重新加载结果
        """
        try:
            old_count = len(self.file_pairs)
            self.file_pairs = self._load_config()
            new_count = len(self.file_pairs)

            return {
                "status": "success",
                "message": "配置重新加载成功",
                "data": {
                    "old_count": old_count,
                    "new_count": new_count,
                    "config_file": self.config_file,
                    "supported_types": list(self.file_pairs.keys())
                }
            }

        except Exception as e:
            self.logger.error(f"重新加载配置失败: {str(e)}")
            return {
                "status": "error",
                "message": f"重新加载配置失败: {str(e)}"
            }
