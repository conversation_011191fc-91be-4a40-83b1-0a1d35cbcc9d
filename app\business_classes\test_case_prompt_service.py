"""
集测提示词和参考文件服务
======================

提供动态的提示词拼接和参考文件内容，供Trae端使用
Trae端负责swagger解析和yml文件生成
"""
import os
from typing import Dict, Any

from app.services.base_service import BaseService


class TestCasePromptService(BaseService):
    """
    集测提示词和参考文件服务
    
    功能：
    1. 动态读取提示词文件
    2. 提供参考文件内容
    3. 拼接完整的提示词
    4. 支持多种测试场景
    
    架构说明：
    - 服务端：只负责提示词拼接和参考文件提供
    - Trae端：负责swagger解析和yml文件写入
    """
    
    def __init__(self):
        super().__init__()
        self.prompt_dir = "app/testCasePromptAndFiles"
        self._ensure_prompt_dir_exists()
    
    def _ensure_prompt_dir_exists(self):
        """确保提示词目录存在"""
        if not os.path.exists(self.prompt_dir):
            self.logger.warning(f"提示词目录不存在: {self.prompt_dir}")
    
    def _read_file_content(self, filename: str) -> str:
        """读取文件内容"""
        try:
            file_path = os.path.join(self.prompt_dir, filename)
            if not os.path.exists(file_path):
                self.logger.error(f"文件不存在: {file_path}")
                return ""
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                self.logger.info(f"成功读取文件: {filename}")
                return content
        except Exception as e:
            self.logger.error(f"读取文件失败 {filename}: {str(e)}")
            return ""
    
    def 获取枚举值集测提示词和参考文件(self) -> Dict[str, Any]:
        """
        🚀 获取枚举值集测的完整提示词和参考文件
        
        供Trae端使用，包含：
        1. 枚举值提取和用例生成的提示词
        2. 标准化用例参考格式
        3. 使用说明
        
        :return: 完整的提示词和参考文件内容
        """
        try:
            # 读取枚举值提示词
            enum_prompt = self._read_file_content("枚举值提示词.txt")
            
            # 读取标准化用例参考格式
            case_template = self._read_file_content("标准化用例参考格式.yml")
            
            # 拼接完整提示词
            full_prompt = self._build_enum_test_prompt(enum_prompt, case_template)
            
            return {
                "status": "success",
                "message": "枚举值集测提示词获取成功",
                "data": {
                    "prompt_type": "枚举值集测",
                    "full_prompt": full_prompt,
                    "components": {
                        "enum_prompt": enum_prompt,
                        "case_template": case_template
                    },
                    "usage_guide": {
                        "description": "在Trae中使用此提示词进行枚举值集测用例生成",
                        "steps": [
                            "1. 复制full_prompt作为系统提示词",
                            "2. 提供swagger接口文档",
                            "3. 提供标准化入参",
                            "4. AI会自动生成HttpRunner格式的yml测试用例"
                        ],
                        "architecture": {
                            "server_role": "提供提示词和参考文件",
                            "trae_role": "解析swagger文档，生成并写入yml文件"
                        }
                    }
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取枚举值集测提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取失败: {str(e)}",
                "troubleshooting": [
                    "检查提示词文件是否存在",
                    "确认文件编码为UTF-8",
                    "查看详细错误日志"
                ]
            }
    
    def 获取必填参数集测提示词和参考文件(self) -> Dict[str, Any]:
        """
        🚀 获取必填参数集测的完整提示词和参考文件
        
        :return: 完整的提示词和参考文件内容
        """
        try:
            # 读取必填参数提示词
            required_prompt = self._read_file_content("必填参数提示词.txt")
            
            # 读取标准化用例参考格式
            case_template = self._read_file_content("标准化用例参考格式.yml")
            
            # 拼接完整提示词
            full_prompt = self._build_required_test_prompt(required_prompt, case_template)
            
            return {
                "status": "success",
                "message": "必填参数集测提示词获取成功",
                "data": {
                    "prompt_type": "必填参数集测",
                    "full_prompt": full_prompt,
                    "components": {
                        "required_prompt": required_prompt,
                        "case_template": case_template
                    },
                    "usage_guide": {
                        "description": "在Trae中使用此提示词进行必填参数集测用例生成",
                        "focus": "专注于必填参数缺失场景的测试用例生成"
                    }
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取必填参数集测提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取失败: {str(e)}"
            }
    
    def 获取接口生成提示词和参考文件(self) -> Dict[str, Any]:
        """
        🚀 获取接口生成的完整提示词和参考文件
        
        :return: 完整的提示词和参考文件内容
        """
        try:
            # 读取接口生成提示词
            api_prompt = self._read_file_content("接口生成提示词.txt")
            
            # 读取标准接口参考格式
            api_template = self._read_file_content("标准接口参考格式.yml")
            
            # 拼接完整提示词
            full_prompt = self._build_api_generation_prompt(api_prompt, api_template)
            
            return {
                "status": "success",
                "message": "接口生成提示词获取成功",
                "data": {
                    "prompt_type": "接口生成",
                    "full_prompt": full_prompt,
                    "components": {
                        "api_prompt": api_prompt,
                        "api_template": api_template
                    },
                    "usage_guide": {
                        "description": "在Trae中使用此提示词进行接口文件生成",
                        "focus": "根据swagger文档自动生成标准接口文件"
                    }
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取接口生成提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取失败: {str(e)}"
            }
    
    def _build_enum_test_prompt(self, enum_prompt: str, case_template: str) -> str:
        """构建枚举值测试的完整提示词"""
        return f"""请帮我设计接口测试用例，要求如下：

{enum_prompt}

用例格式参考：
```yaml
{case_template}
```

请严格按照上述要求和参考格式生成测试用例。只输出最终的测试用例，不输出任何解释说明。
"""
    
    def _build_required_test_prompt(self, required_prompt: str, case_template: str) -> str:
        """构建必填参数测试的完整提示词"""
        return f"""请帮我设计接口测试用例，要求如下：

{required_prompt}

用例格式参考：
```yaml
{case_template}
```

请严格按照上述要求和参考格式生成测试用例。
"""
    
    def _build_api_generation_prompt(self, api_prompt: str, api_template: str) -> str:
        """构建接口生成的完整提示词"""
        return f"""请帮我创建接口文件，要求如下：

{api_prompt}

接口格式参考：
```yaml
{api_template}
```

请严格按照上述要求和参考格式创建接口文件。
"""
    
    def 获取所有提示词类型(self) -> Dict[str, Any]:
        """
        获取所有支持的提示词类型
        
        :return: 支持的提示词类型列表
        """
        return {
            "status": "success",
            "data": {
                "supported_types": [
                    {
                        "type": "枚举值集测",
                        "method": "获取枚举值集测提示词和参考文件",
                        "description": "自动提取枚举参数并生成测试用例",
                        "files": ["枚举值提示词.txt", "标准化用例参考格式.yml"]
                    },
                    {
                        "type": "必填参数集测", 
                        "method": "获取必填参数集测提示词和参考文件",
                        "description": "生成必填参数缺失场景的测试用例",
                        "files": ["必填参数提示词.txt", "标准化用例参考格式.yml"]
                    },
                    {
                        "type": "接口生成",
                        "method": "获取接口生成提示词和参考文件", 
                        "description": "根据swagger文档生成标准接口文件",
                        "files": ["接口生成提示词.txt", "标准接口参考格式.yml"]
                    }
                ],
                "architecture": {
                    "server_responsibility": [
                        "动态读取提示词文件",
                        "拼接完整的提示词",
                        "提供参考文件内容",
                        "不进行swagger解析"
                    ],
                    "trae_responsibility": [
                        "解析swagger文档",
                        "生成yml测试用例",
                        "写入文件到本地",
                        "管理测试用例文件"
                    ]
                },
                "usage_flow": [
                    "1. 选择需要的提示词类型",
                    "2. 调用对应的方法获取完整提示词",
                    "3. 在Trae中使用full_prompt作为系统提示词",
                    "4. 提供swagger文档和相关参数",
                    "5. AI自动生成对应的测试用例或接口文件",
                    "6. Trae负责将生成的内容写入yml文件"
                ]
            }
        }
    
    def 获取提示词文件列表(self) -> Dict[str, Any]:
        """
        获取提示词目录下的所有文件列表
        
        :return: 文件列表和状态
        """
        try:
            if not os.path.exists(self.prompt_dir):
                return {
                    "status": "error",
                    "message": f"提示词目录不存在: {self.prompt_dir}"
                }
            
            files = []
            for filename in os.listdir(self.prompt_dir):
                file_path = os.path.join(self.prompt_dir, filename)
                if os.path.isfile(file_path):
                    file_info = {
                        "filename": filename,
                        "size": os.path.getsize(file_path),
                        "type": "提示词" if filename.endswith('.txt') else "参考文件" if filename.endswith('.yml') else "其他"
                    }
                    files.append(file_info)
            
            return {
                "status": "success",
                "data": {
                    "directory": self.prompt_dir,
                    "total_files": len(files),
                    "files": files
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取文件列表失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取文件列表失败: {str(e)}"
            }
    
    def 获取单个文件内容(self, 文件名: str) -> Dict[str, Any]:
        """
        获取指定文件的内容
        
        :param 文件名: 要读取的文件名
        :return: 文件内容
        """
        try:
            content = self._read_file_content(文件名)
            if not content:
                return {
                    "status": "error",
                    "message": f"文件不存在或内容为空: {文件名}"
                }
            
            return {
                "status": "success",
                "data": {
                    "filename": 文件名,
                    "content": content,
                    "length": len(content),
                    "type": "提示词" if 文件名.endswith('.txt') else "参考文件" if 文件名.endswith('.yml') else "其他"
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取文件内容失败 {文件名}: {str(e)}")
            return {
                "status": "error",
                "message": f"获取文件内容失败: {str(e)}"
            }
