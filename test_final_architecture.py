"""
最终架构验证测试
===============

验证新的业务类自动化MCP架构是否完全正常工作
"""
import requests
import json

def test_final_architecture():
    """测试最终的业务类自动化架构"""
    print("🎉 最终架构验证测试")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 1. 测试业务工具自动发现
    print("1. 🔍 测试业务工具自动发现...")
    try:
        response = requests.get(f"{base_url}/api/ai/mcp/tools")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                tools = data.get("tools", [])
                print(f"   ✅ 自动发现了 {len(tools)} 个业务工具")
                
                # 按业务类分组
                classes = {}
                for tool in tools:
                    class_name = tool.get("class", "unknown")
                    if class_name not in classes:
                        classes[class_name] = []
                    classes[class_name].append(tool["name"])
                
                for class_name, tool_names in classes.items():
                    print(f"   📁 {class_name}: {len(tool_names)} 个方法")
            else:
                print(f"   ❌ 获取工具失败: {data.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False
    
    # 2. 测试直接调用业务方法
    print("\n2. 🔧 测试直接调用业务方法...")
    test_calls = [
        {"tool_name": "生成手机号", "parameters": {"数量": 1}},
        {"tool_name": "生成测试密码", "parameters": {"数量": 1, "长度": 8}}
    ]
    
    for test_call in test_calls:
        try:
            response = requests.post(
                f"{base_url}/api/ai/mcp/call",
                json=test_call,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"   ✅ {test_call['tool_name']} 调用成功")
                else:
                    print(f"   ❌ {test_call['tool_name']} 调用失败: {data.get('error')}")
            else:
                print(f"   ❌ {test_call['tool_name']} HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {test_call['tool_name']} 请求失败: {e}")
    
    # 3. 测试AI自动调用（简单测试）
    print("\n3. 🤖 测试AI自动调用...")
    try:
        payload = {
            "message": "生成一个手机号",
            "use_tools": True
        }
        
        response = requests.post(
            f"{base_url}/api/ai/simple-mcp/chat",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("message"):
                print(f"   ✅ AI调用成功")
                if data.get("tool_calls"):
                    tools_used = [call['function'] for call in data['tool_calls']]
                    print(f"   🔧 AI自动调用了: {tools_used}")
                else:
                    print(f"   ⚠️ AI没有调用工具")
            else:
                print(f"   ❌ AI调用失败")
        else:
            print(f"   ❌ AI调用HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ AI调用请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 最终架构验证完成！")
    
    print("\n🏗️ 新架构总结:")
    print("   ✅ 极简业务类定义 - 只需继承BusinessBase")
    print("   ✅ 自动工具生成 - 无需手动配置")
    print("   ✅ 自动异步处理 - 业务类自动处理异步")
    print("   ✅ 自动错误处理 - 统一错误捕获和返回")
    print("   ✅ 自动类型解析 - 自动解析方法参数类型")
    print("   ✅ 自动元数据 - 从Meta类自动读取模块信息")
    print("   ✅ AI智能调用 - AI可以准确选择和调用方法")
    
    print("\n📝 使用方法:")
    print("   1. 在 app/business_classes/ 中定义业务类")
    print("   2. 继承 BusinessBase，添加 Meta 类")
    print("   3. 实现业务方法，使用类型注解")
    print("   4. 重启服务")
    print("   5. AI立即可以使用新方法！")
    
    print("\n🚀 架构优势:")
    print("   🔥 零配置 - 完全自动化")
    print("   📁 就近定义 - 业务逻辑和元数据在一起")
    print("   🤖 智能提示 - AI能准确理解业务功能")
    print("   ⚡ 即插即用 - 新增业务类立即生效")
    print("   🛡️ 类型安全 - 自动类型检查和转换")
    print("   🌐 统一接口 - 所有业务方法使用相同API")
    
    return True

if __name__ == "__main__":
    test_final_architecture()
