"""
MCP健康检查和自动重连服务
=============================

解决MCP服务重启后需要手动刷新的问题
"""
import asyncio
import logging
import time
from typing import Dict, Any, Optional
import httpx
from datetime import datetime, timedelta

from app.core.config import settings
from app.services.base_service import BaseService


class MCPHealthService(BaseService):
    """
    MCP健康检查和自动重连服务
    
    功能：
    1. 定期检查MCP服务健康状态
    2. 自动重连断开的MCP连接
    3. 缓存失效和刷新机制
    4. 连接池管理
    """
    
    def __init__(self):
        super().__init__()
        self.mcp_base_url = "http://localhost:8000/api/ai/mcp"
        self.health_check_interval = 30  # 30秒检查一次
        self.connection_timeout = 10  # 连接超时时间
        self.retry_attempts = 3  # 重试次数
        self.retry_delay = 5  # 重试延迟
        
        # 连接状态管理
        self._is_healthy = False
        self._last_health_check = None
        self._connection_pool = None
        self._tools_cache = None
        self._cache_expiry = None
        self._cache_duration = 300  # 缓存5分钟
        
        # 启动健康检查任务
        self._health_check_task = None
        self._start_health_monitoring()
    
    def _start_health_monitoring(self):
        """启动健康监控任务"""
        if self._health_check_task is None:
            self._health_check_task = asyncio.create_task(self._health_monitor_loop())
            self.logger.info("🔍 MCP健康监控已启动")
    
    async def _health_monitor_loop(self):
        """健康监控循环"""
        while True:
            try:
                await self.check_mcp_health()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                self.logger.error(f"健康监控循环异常: {str(e)}")
                await asyncio.sleep(self.health_check_interval)
    
    async def check_mcp_health(self) -> Dict[str, Any]:
        """
        检查MCP服务健康状态
        
        :return: 健康状态信息
        """
        try:
            start_time = time.time()
            
            # 创建新的HTTP客户端进行健康检查
            async with httpx.AsyncClient(timeout=self.connection_timeout) as client:
                response = await client.get(f"{self.mcp_base_url}/tools")
                response.raise_for_status()
                
                data = response.json()
                response_time = time.time() - start_time
                
                if data.get("success"):
                    self._is_healthy = True
                    self._last_health_check = datetime.now()
                    
                    # 更新工具缓存
                    self._tools_cache = data.get("tools", [])
                    self._cache_expiry = datetime.now() + timedelta(seconds=self._cache_duration)
                    
                    self.logger.debug(f"✅ MCP服务健康检查通过，响应时间: {response_time:.2f}s")
                    
                    return {
                        "status": "healthy",
                        "response_time": response_time,
                        "tools_count": len(self._tools_cache),
                        "last_check": self._last_health_check.isoformat(),
                        "cache_valid": True
                    }
                else:
                    self._is_healthy = False
                    self.logger.warning(f"⚠️ MCP服务返回错误: {data.get('error')}")
                    return {
                        "status": "unhealthy",
                        "error": data.get('error'),
                        "response_time": response_time
                    }
                    
        except Exception as e:
            self._is_healthy = False
            self.logger.error(f"❌ MCP健康检查失败: {str(e)}")
            
            # 尝试自动重连
            await self._attempt_reconnection()
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
    
    async def _attempt_reconnection(self):
        """尝试自动重连MCP服务"""
        self.logger.info("🔄 尝试自动重连MCP服务...")
        
        for attempt in range(self.retry_attempts):
            try:
                await asyncio.sleep(self.retry_delay)
                
                # 清除缓存
                self._invalidate_cache()
                
                # 重新检查健康状态
                health_result = await self.check_mcp_health()
                
                if health_result.get("status") == "healthy":
                    self.logger.info(f"✅ MCP服务重连成功 (尝试 {attempt + 1}/{self.retry_attempts})")
                    return True
                    
            except Exception as e:
                self.logger.warning(f"重连尝试 {attempt + 1} 失败: {str(e)}")
        
        self.logger.error(f"❌ MCP服务重连失败，已尝试 {self.retry_attempts} 次")
        return False
    
    def _invalidate_cache(self):
        """使缓存失效"""
        self._tools_cache = None
        self._cache_expiry = None
        self.logger.debug("🗑️ MCP工具缓存已清除")
    
    async def get_tools_with_health_check(self) -> Dict[str, Any]:
        """
        获取工具列表，带健康检查和缓存机制
        
        :return: 工具列表和状态信息
        """
        try:
            # 检查缓存是否有效
            if (self._tools_cache and 
                self._cache_expiry and 
                datetime.now() < self._cache_expiry and 
                self._is_healthy):
                
                self.logger.debug("📦 使用缓存的工具列表")
                return {
                    "success": True,
                    "tools": self._tools_cache,
                    "source": "cache",
                    "cache_expiry": self._cache_expiry.isoformat()
                }
            
            # 缓存失效或服务不健康，重新获取
            self.logger.debug("🔄 重新获取工具列表")
            
            # 先检查健康状态
            health_result = await self.check_mcp_health()
            
            if health_result.get("status") == "healthy":
                return {
                    "success": True,
                    "tools": self._tools_cache,
                    "source": "fresh",
                    "health_check": health_result
                }
            else:
                # 如果服务不健康，尝试使用过期缓存
                if self._tools_cache:
                    self.logger.warning("⚠️ MCP服务不健康，使用过期缓存")
                    return {
                        "success": True,
                        "tools": self._tools_cache,
                        "source": "stale_cache",
                        "warning": "使用过期缓存，MCP服务可能不可用",
                        "health_check": health_result
                    }
                else:
                    return {
                        "success": False,
                        "error": "MCP服务不可用且无缓存数据",
                        "health_check": health_result
                    }
                    
        except Exception as e:
            self.logger.error(f"获取工具列表失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def call_tool_with_retry(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用工具，带重试机制
        
        :param tool_name: 工具名称
        :param parameters: 工具参数
        :return: 调用结果
        """
        last_error = None
        
        for attempt in range(self.retry_attempts):
            try:
                # 检查服务健康状态
                if not self._is_healthy:
                    await self.check_mcp_health()
                
                if not self._is_healthy:
                    raise Exception("MCP服务不健康")
                
                # 调用工具
                async with httpx.AsyncClient(timeout=self.connection_timeout) as client:
                    payload = {
                        "tool_name": tool_name,
                        "parameters": parameters
                    }
                    
                    response = await client.post(
                        f"{self.mcp_base_url}/call",
                        json=payload,
                        headers={"Content-Type": "application/json"}
                    )
                    response.raise_for_status()
                    
                    result = response.json()
                    self.logger.info(f"✅ 工具调用成功: {tool_name}")
                    return result
                    
            except Exception as e:
                last_error = e
                self.logger.warning(f"工具调用尝试 {attempt + 1} 失败: {str(e)}")
                
                if attempt < self.retry_attempts - 1:
                    # 标记服务不健康，触发重连
                    self._is_healthy = False
                    await asyncio.sleep(self.retry_delay)
        
        # 所有重试都失败
        self.logger.error(f"❌ 工具调用最终失败: {tool_name}")
        return {
            "success": False,
            "error": str(last_error),
            "tool_name": tool_name,
            "parameters": parameters,
            "retry_attempts": self.retry_attempts
        }
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        获取连接状态信息
        
        :return: 连接状态
        """
        return {
            "is_healthy": self._is_healthy,
            "last_health_check": self._last_health_check.isoformat() if self._last_health_check else None,
            "cache_valid": self._cache_expiry and datetime.now() < self._cache_expiry,
            "cache_expiry": self._cache_expiry.isoformat() if self._cache_expiry else None,
            "tools_cached": len(self._tools_cache) if self._tools_cache else 0,
            "health_check_interval": self.health_check_interval,
            "connection_timeout": self.connection_timeout,
            "retry_attempts": self.retry_attempts
        }
    
    async def force_refresh(self) -> Dict[str, Any]:
        """
        强制刷新连接和缓存
        
        :return: 刷新结果
        """
        self.logger.info("🔄 强制刷新MCP连接和缓存")
        
        # 清除缓存
        self._invalidate_cache()
        
        # 重新检查健康状态
        health_result = await self.check_mcp_health()
        
        return {
            "refresh_time": datetime.now().isoformat(),
            "health_check": health_result,
            "cache_cleared": True
        }
    
    def __del__(self):
        """清理资源"""
        if self._health_check_task:
            self._health_check_task.cancel()


# 全局MCP健康服务实例
mcp_health_service = MCPHealthService()
