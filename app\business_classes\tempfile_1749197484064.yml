- config:
    name: 创建有账号证书申请任务
    variables:
      bizType: TCLOUD
      operatorType: APPLY
      projectId: "**********"
      notifyUrl: "http://libaohui.com.cn/callback/ding"
      redirectUrl: "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai"
      phone: "19888644846"
      certNum: 1
      certName: "esigntest苍诚经营的个体工商户"
      licenseNumber: "91000000BL0X3BDKXJ"
      userType: 2

# 正向用例：bizType - TIANYIN
- test:
    name: 创建有账号证书申请任务-bizType-TIANYIN
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "TIANYIN",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：bizType - TIANYIN_OFFLINE
- test:
    name: 创建有账号证书申请任务-bizType-TIANYIN_OFFLINE
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "TIANYIN_OFFLINE",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：bizType - ESHIELD
- test:
    name: 创建有账号证书申请任务-bizType-ESHIELD
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "ESHIELD",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：bizType - TCLOUD
- test:
    name: 创建有账号证书申请任务-bizType-TCLOUD
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：bizType - PUBCLOUD
- test:
    name: 创建有账号证书申请任务-bizType-PUBCLOUD
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "PUBCLOUD",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 反向用例：bizType - 非法值（超出范围）
- test:
    name: 创建有账号证书申请任务-bizType-非法值
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "INVALID_BIZ_TYPE",
          "operatorType": "$operatorType",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 400]
      - eq: ["content.message", "参数错误: bizType 不合法"]

# 正向用例：operatorType - APPLY
- test:
    name: 创建有账号证书申请任务-operatorType-APPLY
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "APPLY",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：operatorType - DELAY
- test:
    name: 创建有账号证书申请任务-operatorType-DELAY
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizType": "$bizType",
          "operatorType": "DELAY",
          "projectId": "$projectId",
          "notifyUrl": "$notifyUrl",
          "redirectUrl": "$redirectUrl",
          "phone": "$phone",
          "applyConfigModel": {
            "certNum": "$certNum"
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "$certName",
            "licenseNumber": "$licenseNumber",
            "licenseType": 1,
            "userType": "$userType"
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 正向用例：operatorType - UPDATE
- test:
    name: 创建有账号证书申请任务-operatorType-UPDATE
    api: /v1/certs/create-cert-task
    variables:
      json:
        {
          "bizT