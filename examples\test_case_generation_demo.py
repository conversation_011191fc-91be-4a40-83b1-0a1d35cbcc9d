"""
集测用例生成演示
================

演示如何使用集测用例生成服务
"""
import asyncio
import json
from app.business_classes.test_case_generator import TestCaseGenerator


async def demo_basic_usage():
    """基础使用演示"""
    print("🚀 集测用例生成演示 - 基础用法")
    print("=" * 50)
    
    # 创建生成器实例
    generator = TestCaseGenerator()
    
    # 示例接口文档
    api_doc = """
**接口地址** `/v1/certs/create-cert-task`
**请求方式** `POST`
**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| param         |      param   |     body        |       true      | CertCreateTaskRequest   | CertCreateTaskRequest     |

**schema属性说明**

**CertCreateTaskRequest**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| bizType  | 业务类型，可选值：TCLOUD、ESIGN |   body    |   true   |string  |       |
| operatorType  | 操作类型，1、申请 2、撤销 |   body    |   true   |int  |       |
| projectId  | 项目ID |   body    |   true   |string  |       |
| userType  | 用户类型，1、个人 2、企业 |   body    |   false   |int  |       |
| applyUserModel  | 申请用户信息 |   body    |   false   |ApplyUserModel  | ApplyUserModel      |

**ApplyUserModel**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| licenseType  | 证件类型，1、营业执照 2、组织机构代码证 3、统一社会信用代码 |   body    |   false   |int  |       |
| certName  | 证书名称 |   body    |   false   |string  |       |
"""
    
    # 标准化入参
    standard_params = {
        'bizType': 'TCLOUD',
        'operatorType': 1,
        'projectId': '**********',
        'userType': 2,
        'applyUserModel': {
            'licenseType': 1,
            'certName': 'esigntest苍诚经营的个体工商户'
        }
    }
    
    # 生成测试用例
    result = generator.生成枚举集测用例(
        接口文档=api_doc,
        标准化入参=json.dumps(standard_params, ensure_ascii=False)
    )
    
    # 输出结果
    if result["status"] == "success":
        data = result["data"]
        print(f"✅ 成功生成 {data['test_cases_count']} 个测试用例")
        print(f"📊 发现 {data['enum_params_found']} 个枚举参数")
        print("\n🔍 发现的枚举参数：")
        for param in data["enum_params"]:
            print(f"  - {param['parameter_path']}: {param['enum_values']}")
        
        print("\n📝 生成的HttpRunner yml文件：")
        print("-" * 30)
        print(data["yml_content"])
        print("-" * 30)
        
        print("\n💡 使用指南：")
        for key, value in data["usage_guide"].items():
            print(f"  {key}: {value}")
    else:
        print(f"❌ 生成失败: {result['message']}")


async def demo_advanced_usage():
    """高级用法演示"""
    print("\n🚀 集测用例生成演示 - 高级用法")
    print("=" * 50)
    
    generator = TestCaseGenerator()
    
    # 复杂接口文档示例
    complex_api_doc = """
**接口地址** `/v1/certs/batch-create`
**请求方式** `POST`
**请求参数**

| 参数名称 | 说明 | 参数类型 | 是否必须 | 类型 | schema |
|----------|------|----------|----------|------|--------|
| batchRequest | 批量请求参数 | body | true | BatchCreateRequest | BatchCreateRequest |

**BatchCreateRequest**

| 参数名称 | 说明 | 参数类型 | 是否必须 | 类型 | schema |
|----------|------|----------|----------|------|--------|
| batchType | 批量类型，可选：SYNC、ASYNC、SCHEDULE | body | true | string | |
| priority | 优先级，取值范围：1-10 | body | false | int | |
| config | 配置信息 | body | true | BatchConfig | BatchConfig |

**BatchConfig**

| 参数名称 | 说明 | 参数类型 | 是否必须 | 类型 | schema |
|----------|------|----------|----------|------|--------|
| retryPolicy | 重试策略，NONE、LINEAR、EXPONENTIAL | body | false | string | |
| maxRetries | 最大重试次数，1、2、3、5、10 | body | false | int | |
| timeout | 超时时间，可选值：30、60、120、300 | body | false | int | |
"""
    
    complex_params = {
        'batchType': 'SYNC',
        'priority': 5,
        'config': {
            'retryPolicy': 'LINEAR',
            'maxRetries': 3,
            'timeout': 60
        }
    }
    
    result = generator.生成枚举集测用例(
        接口文档=complex_api_doc,
        标准化入参=json.dumps(complex_params, ensure_ascii=False),
        接口地址="/v1/certs/batch-create",
        请求方式="POST"
    )
    
    if result["status"] == "success":
        data = result["data"]
        print(f"✅ 复杂接口生成 {data['test_cases_count']} 个测试用例")
        print(f"📊 发现 {data['enum_params_found']} 个枚举参数")
        
        print("\n🔍 嵌套枚举参数：")
        for param in data["enum_params"]:
            if "." in param['parameter_path']:
                print(f"  - {param['parameter_path']}: {param['enum_values']}")
        
        # 显示部分测试用例
        print("\n📋 部分测试用例：")
        for i, case in enumerate(data["test_cases"][:3]):
            print(f"  {i+1}. {case['description']}")
            print(f"     参数: {case['enum_info']['parameter']} = {case['enum_info']['value']}")


async def demo_enum_types():
    """枚举类型支持演示"""
    print("\n🚀 支持的枚举类型演示")
    print("=" * 50)
    
    generator = TestCaseGenerator()
    
    # 获取支持的枚举类型
    result = generator.获取支持的枚举类型()
    
    if result["status"] == "success":
        data = result["data"]
        print("📋 支持的枚举类型：")
        for enum_type in data["supported_enum_types"]:
            print(f"\n  🔸 {enum_type['type']}")
            print(f"    示例: {enum_type['example']}")
            print(f"    模式: {enum_type['pattern']}")
        
        print("\n📏 提取规则：")
        for rule in data["extraction_rules"]:
            print(f"  - {rule}")


async def demo_template_generation():
    """模板生成演示"""
    print("\n🚀 用例模板生成演示")
    print("=" * 50)
    
    generator = TestCaseGenerator()
    
    # 生成用例模板
    result = generator.生成用例模板("证书申请接口")
    
    if result["status"] == "success":
        data = result["data"]
        print("📝 生成的HttpRunner模板：")
        print("-" * 30)
        print(data["yml_content"])
        print("-" * 30)
        print(f"\n💡 使用说明: {data['usage']}")


async def demo_error_handling():
    """错误处理演示"""
    print("\n🚀 错误处理演示")
    print("=" * 50)
    
    generator = TestCaseGenerator()
    
    # 测试错误的输入
    print("1. 测试无效的JSON格式：")
    result = generator.生成枚举集测用例(
        接口文档="简单文档",
        标准化入参="无效的JSON格式"
    )
    print(f"   结果: {result['status']} - {result['message']}")
    
    print("\n2. 测试空的接口文档：")
    result = generator.生成枚举集测用例(
        接口文档="",
        标准化入参="{}"
    )
    print(f"   结果: {result['status']} - {result['message']}")
    
    print("\n3. 测试无枚举参数的文档：")
    simple_doc = """
**接口地址** `/test`
**请求方式** `GET`
**请求参数**

| 参数名称 | 说明 | 参数类型 | 是否必须 | 类型 | schema |
|----------|------|----------|----------|------|--------|
| name | 用户名 | query | true | string | |
| age | 年龄 | query | false | int | |
"""
    result = generator.生成枚举集测用例(
        接口文档=simple_doc,
        标准化入参='{"name": "test", "age": 25}'
    )
    if result["status"] == "success":
        print(f"   结果: 成功，但生成了 {result['data']['test_cases_count']} 个测试用例")
    else:
        print(f"   结果: {result['status']} - {result['message']}")


async def main():
    """主演示函数"""
    print("🎯 集测用例生成服务完整演示")
    print("=" * 60)
    
    await demo_basic_usage()
    await demo_advanced_usage()
    await demo_enum_types()
    await demo_template_generation()
    await demo_error_handling()
    
    print("\n🎉 演示完成！")
    print("\n📖 使用指南：")
    print("1. 在Trae中配置MCP服务地址: http://localhost:8000/api/ai/mcp")
    print("2. 直接输入接口文档和标准化入参即可生成测试用例")
    print("3. 无需再输入长篇提示词或引用模板文件")
    print("4. 系统会自动生成HttpRunner格式的yml测试用例")


if __name__ == "__main__":
    asyncio.run(main())
