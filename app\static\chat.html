<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 智能助手 - 多轮对话</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .chat-header .subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            order: 2;
        }

        .message.ai .message-avatar {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .message-time {
            font-size: 12px;
            opacity: 0.6;
            margin-top: 5px;
        }

        .tool-calls {
            margin-top: 10px;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .tool-call {
            font-size: 12px;
            margin: 5px 0;
            color: #667eea;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 15px 20px;
            background: white;
            border-radius: 20px;
            border-bottom-left-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .clear-chat {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .clear-chat:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 90vh;
                border-radius: 15px;
            }

            .message-content {
                max-width: 85%;
            }

            .chat-header h1 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <button class="clear-chat" onclick="clearChat()">清空对话</button>
            <div class="status-indicator"></div>
            <h1>🤖 AI 智能助手</h1>
            <div class="subtitle">支持多轮对话 · 智能工具调用</div>
        </div>

        <div class="chat-messages" id="chat-messages">
            <div class="message ai">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div>你好！我是AI智能助手，可以帮助您完成各种任务。</div>
                    <div>💡 我可以：</div>
                    <div>• 📜 管理证书申请和查询</div>
                    <div>• 👤 处理用户信息</div>
                    <div>• 🎲 生成各种测试数据</div>
                    <div>• ⚙️ 提供实用工具功能</div>
                    <div class="message-time" id="welcome-time"></div>
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typing-indicator">
            <div class="message-avatar">🤖</div>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <textarea
                    id="chat-input"
                    class="chat-input"
                    placeholder="请输入您的问题..."
                    rows="1"
                ></textarea>
                <button id="send-button" class="send-button">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let conversationHistory = [];
        let isTyping = false;

        // DOM 元素
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置欢迎消息时间
            document.getElementById('welcome-time').textContent = formatTime(new Date());

            // 自动调整输入框高度
            chatInput.addEventListener('input', autoResizeTextarea);

            // 回车发送消息
            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 发送按钮点击事件
            sendButton.addEventListener('click', sendMessage);

            // 聚焦输入框
            chatInput.focus();
        });

        // 自动调整文本域高度
        function autoResizeTextarea() {
            chatInput.style.height = 'auto';
            chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
        }

        // 格式化时间
        function formatTime(date) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 添加消息到聊天界面
        function addMessage(role, content, toolCalls = null, timestamp = new Date()) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            const avatar = role === 'user' ? '👤' : '🤖';
            const time = formatTime(timestamp);

            let toolCallsHtml = '';
            if (toolCalls && toolCalls.length > 0) {
                toolCallsHtml = '<div class="tool-calls">';
                toolCallsHtml += '<div style="font-weight: bold; margin-bottom: 5px;">🔧 工具调用:</div>';
                toolCalls.forEach(call => {
                    toolCallsHtml += `<div class="tool-call">• ${call.function}(${JSON.stringify(call.arguments)})</div>`;
                });
                toolCallsHtml += '</div>';
            }

            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div>${content}</div>
                    ${toolCallsHtml}
                    <div class="message-time">${time}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();

            // 添加到对话历史
            conversationHistory.push({
                role: role === 'user' ? 'user' : 'assistant',
                content: content
            });
        }

        // 显示打字指示器
        function showTypingIndicator() {
            typingIndicator.style.display = 'flex';
            scrollToBottom();
        }

        // 隐藏打字指示器
        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        // 滚动到底部
        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 发送消息
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message || isTyping) return;

            // 添加用户消息
            addMessage('user', message);

            // 清空输入框
            chatInput.value = '';
            autoResizeTextarea();

            // 设置发送状态
            isTyping = true;
            sendButton.disabled = true;
            showTypingIndicator();

            try {
                // 发送请求
                const response = await fetch('/api/ai/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_history: conversationHistory.slice(-10), // 保留最近10轮对话
                        use_tools: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 隐藏打字指示器
                hideTypingIndicator();

                // 添加AI回复
                addMessage('ai', data.message || '抱歉，我没有收到有效的回复。', data.tool_calls);

            } catch (error) {
                console.error('发送消息失败:', error);
                hideTypingIndicator();
                addMessage('ai', `❌ 抱歉，发送消息时出现错误：${error.message}`);
            } finally {
                // 重置状态
                isTyping = false;
                sendButton.disabled = false;
                chatInput.focus();
            }
        }

        // 清空对话
        function clearChat() {
            if (confirm('确定要清空所有对话记录吗？')) {
                // 清空对话历史
                conversationHistory = [];

                // 清空聊天界面，保留欢迎消息
                chatMessages.innerHTML = `
                    <div class="message ai">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                            <div>对话已清空！我是AI智能助手，可以帮助您完成各种任务。</div>
                            <div>💡 我可以：</div>
                            <div>• 📜 管理证书申请和查询</div>
                            <div>• 👤 处理用户信息</div>
                            <div>• 🎲 生成各种测试数据</div>
                            <div>• ⚙️ 提供实用工具功能</div>
                            <div class="message-time">${formatTime(new Date())}</div>
                        </div>
                    </div>
                `;

                chatInput.focus();
            }
        }
    </script>
</body>
</html>
