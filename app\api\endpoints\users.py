"""
用户相关API端点
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging

from app.services.user_service import UserService
from app.schemas.user_schemas import UserInfoResponse

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/{env}", 
           operation_id="get_user_or_enterprise_info",
           description="获取用户或企业信息",
           response_model=UserInfoResponse)
async def get_user_info(env: int) -> Dict[str, Any]:
    """
    获取用户或企业信息
    
    :param env: 枚举值：1、测试环境 2、模拟环境
    :return: 用户或企业信息
    """
    try:
        user_service = UserService()
        result = await user_service.get_user_or_enterprise_info(env)
        return result
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户信息失败: {str(e)}")

@router.get("/mock/{user_type}",
           description="生成模拟用户数据")
async def generate_mock_user(user_type: str) -> Dict[str, Any]:
    """
    生成模拟用户数据
    
    :param user_type: 用户类型 (personal/enterprise)
    :return: 模拟用户数据
    """
    try:
        user_service = UserService()
        result = await user_service.generate_mock_user(user_type)
        return result
    except Exception as e:
        logger.error(f"生成模拟用户数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成模拟用户数据失败: {str(e)}")
