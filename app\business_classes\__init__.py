"""
业务类自动MCP工具生成
===================

🎯 设计目标：
- 只需要定义业务类，系统自动生成MCP工具
- 自动处理异步调用、错误处理、类型转换
- AI可以直接调用业务类的方法
- 完全自动化，无需任何手动配置

📁 目录结构：
app/business_classes/
├── __init__.py              # 业务类自动发现
├── user_business.py         # 用户业务类
├── certificate_business.py  # 证书业务类
├── data_generator.py        # 数据生成业务类
└── custom_tools.py          # 自定义工具类

🚀 使用方法：
1. 定义业务类，继承 BusinessBase
2. 添加业务方法，使用类型注解
3. 重启服务
4. AI立即可以使用新方法！

📝 业务类规范：
class YourBusiness(BusinessBase):
    '''业务类描述'''
    
    class Meta:
        name = "业务名称"
        icon = "📁"
        description = "业务描述"
    
    def your_method(self, param: str) -> Dict[str, Any]:
        '''方法描述'''
        return {"status": "success", "data": "结果"}

✨ 特性：
- 自动异步处理
- 自动错误捕获
- 自动类型转换
- 自动MCP工具生成
- 统一返回格式
"""

import inspect
import asyncio
import concurrent.futures
from typing import Dict, Any, List, Type
from abc import ABC

# 业务基类
class BusinessBase(ABC):
    """
    业务基类
    
    所有业务类都应该继承这个基类
    """
    
    class Meta:
        name = "基础业务"
        icon = "📁"
        description = "业务基类"
        category = "core"
    
    def __init__(self):
        self.logger = self._get_logger()
    
    def _get_logger(self):
        """获取日志记录器"""
        import logging
        return logging.getLogger(self.__class__.__name__)
    
    def _run_async_method(self, coro):
        """
        在新线程中运行异步方法
        
        :param coro: 协程对象
        :return: 执行结果
        """
        def run_in_thread():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(coro)
            finally:
                loop.close()
        
        with concurrent.futures.ThreadPoolExecutor() as executor:
            return executor.submit(run_in_thread).result()

# 导入所有业务类
from . import user_business
from . import certificate_business
from . import data_generator
from . import custom_tools
from . import test_case_prompt_service

# 业务类模块列表
BUSINESS_CLASS_MODULES = [
    user_business,
    certificate_business,
    data_generator,
    custom_tools,
    test_case_prompt_service
]

def get_all_business_classes() -> List[Type[BusinessBase]]:
    """
    获取所有业务类
    
    :return: 业务类列表
    """
    classes = []
    for module in BUSINESS_CLASS_MODULES:
        for name, cls in inspect.getmembers(module, inspect.isclass):
            # 只获取继承自BusinessBase的类，排除BusinessBase本身
            if (issubclass(cls, BusinessBase) and 
                cls != BusinessBase and 
                cls.__module__ == module.__name__):
                classes.append(cls)
    
    return classes

def get_all_business_methods():
    """
    获取所有业务方法
    
    :return: (method_name, class_instance, method) 列表
    """
    methods = []
    business_classes = get_all_business_classes()
    
    for cls in business_classes:
        instance = cls()  # 创建实例
        
        for method_name, method in inspect.getmembers(instance, inspect.ismethod):
            # 跳过私有方法和基类方法
            if (not method_name.startswith('_') and 
                hasattr(cls, method_name) and
                method_name not in ['_get_logger', '_run_async_method']):
                methods.append((method_name, instance, method))
    
    return methods

def get_business_class_metadata():
    """
    获取所有业务类的元数据
    
    :return: 业务类元数据字典
    """
    metadata = {}
    business_classes = get_all_business_classes()
    
    for cls in business_classes:
        class_name = cls.__name__
        
        # 获取类的Meta信息
        if hasattr(cls, 'Meta'):
            meta = cls.Meta
            metadata[class_name] = {
                "name": getattr(meta, 'name', class_name),
                "icon": getattr(meta, 'icon', '📁'),
                "description": getattr(meta, 'description', f'{class_name} 业务类'),
                "category": getattr(meta, 'category', 'general'),
                "ai_prompt_hint": getattr(meta, 'ai_prompt_hint', f'当用户需要 {class_name} 相关功能时，请使用此类的方法')
            }
        else:
            # 默认元数据
            metadata[class_name] = {
                "name": class_name,
                "icon": "📁",
                "description": f"{class_name} 业务类",
                "category": "general",
                "ai_prompt_hint": f"当用户需要 {class_name} 相关功能时，请使用此类的方法"
            }
    
    return metadata

def get_business_methods_with_metadata():
    """
    获取带有元数据的业务方法
    
    :return: (methods, metadata) 元组
    """
    methods = get_all_business_methods()
    metadata = get_business_class_metadata()
    return methods, metadata
