# Trae MCP 配置指南

## 🎯 问题解决

### 问题描述
在 Trae 中配置了 MCP 的 HTTP 地址，但服务列表中看不到提示词相关的 MCP 工具。

### 根本原因
系统有两套不同的 MCP 实现方式：

1. **HTTP API 方式** (`/api/ai/mcp/tools`) - 自定义实现
2. **标准 MCP 协议** (`/mcp`) - FastApiMCP 标准实现

Trae 使用的是标准 MCP 协议，需要配置正确的端点。

## ✅ 解决方案

### 1. 正确的 MCP 配置

在 Trae 中配置 MCP 服务时，使用以下地址：

```
http://localhost:8000/mcp
```

**不要使用**：
- ❌ `http://localhost:8000/api/ai/mcp`
- ❌ `http://localhost:8000/api/ai/mcp/tools`

### 2. 验证配置

启动服务后，可以通过以下方式验证：

#### 方式1：检查 OpenAPI 文档
访问：`http://localhost:8000/openapi.json`

查找以下路径：
- `/api/business/获取枚举值集测提示词和参考文件`
- `/api/business/获取必填参数集测提示词和参考文件`
- `/api/business/获取接口生成提示词和参考文件`
- `/api/business/获取所有提示词类型`
- `/api/business/获取通用提示词`

#### 方式2：直接测试业务路由
```bash
curl -X POST "http://localhost:8000/api/business/获取枚举值集测提示词和参考文件"
```

## 📋 可用的提示词工具

配置成功后，在 Trae 的 MCP 服务列表中应该能看到以下工具：

### 📝 集测提示词服务

1. **获取枚举值集测提示词和参考文件**
   - 功能：获取枚举值集测的完整提示词和参考文件
   - 参数：无
   - 返回：完整的动态提示词（2161字符）

2. **获取必填参数集测提示词和参考文件**
   - 功能：获取必填参数集测的完整提示词和参考文件
   - 参数：无
   - 返回：必填参数测试提示词

3. **获取接口生成提示词和参考文件**
   - 功能：获取接口生成的完整提示词和参考文件
   - 参数：无
   - 返回：接口生成提示词

4. **获取所有提示词类型**
   - 功能：获取所有支持的提示词类型列表
   - 参数：无
   - 返回：支持的提示词类型和描述

5. **获取通用提示词**
   - 功能：通用的提示词获取方法，支持任意类型
   - 参数：`提示词类型` (string)
   - 返回：指定类型的提示词

### 📜 证书管理工具

6. **创建证书**
7. **查询证书详情**
8. **更新证书**
9. **吊销证书**
10. **获取测试账号**
11. **批量创建证书**
12. **证书完整流程测试**

### 👤 用户管理工具

13. **生成模拟用户**

## 🔧 技术架构

### 两套 MCP 实现的区别

| 特性 | HTTP API 方式 | 标准 MCP 协议 |
|------|---------------|---------------|
| 端点 | `/api/ai/mcp/tools` | `/mcp` |
| 实现方式 | 自定义 HTTP 接口 | FastApiMCP 标准 |
| 工具发现 | `get_all_business_methods()` | FastAPI 路由自动生成 |
| Trae 兼容性 | ❌ 不兼容 | ✅ 完全兼容 |
| 标准化程度 | 自定义格式 | MCP 标准协议 |

### 解决方案实施

1. **保留业务类自动发现**：`TestCasePromptService` 继承 `BusinessBase`
2. **添加 FastAPI 路由**：在 `business_routes.py` 中手动添加提示词相关路由
3. **双重暴露**：既支持自定义 HTTP API，也支持标准 MCP 协议

## 🚀 使用示例

### 在 Trae 中调用枚举值提示词

配置完成后，在 Trae 中可以直接调用：

```
工具名称：获取枚举值集测提示词和参考文件
参数：无
```

返回的提示词可以直接用于生成枚举值测试用例。

### 提示词内容预览

枚举值集测提示词包含：
- 自动递归提取枚举参数的规则
- 测试用例生成要求
- 完整的 YAML 格式参考
- 测试数据规范
- 特别注意事项

## 🔍 故障排除

### 问题1：Trae 中看不到工具
- **检查**：确认使用 `http://localhost:8000/mcp` 而不是其他地址
- **验证**：访问 OpenAPI 文档确认路由已注册
- **重启**：重启 Trae 和服务

### 问题2：工具调用失败
- **检查**：确认服务正常运行
- **验证**：直接调用业务路由测试
- **日志**：查看服务端日志

### 问题3：提示词内容不完整
- **检查**：确认 `testCasePromptAndFiles` 目录下文件完整
- **验证**：检查配置文件 `prompt_config.yml`
- **重载**：调用重新加载配置方法

## 📈 优势

1. **标准化**：使用 MCP 标准协议，兼容性更好
2. **动态化**：提示词完全基于文件内容，无硬编码
3. **扩展性**：支持动态添加新的提示词类型
4. **维护性**：修改提示词只需编辑文件
5. **可靠性**：多层兜底机制，确保系统稳定
