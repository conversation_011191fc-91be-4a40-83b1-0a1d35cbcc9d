"""
简化的MCP服务 - 通过HTTP接口使用MCP工具
这个服务让AI可以通过一个简单的HTTP地址调用所有MCP功能

🚀 新增功能：
- 自动健康检查和重连
- 智能缓存机制
- 连接池管理
- 故障自动恢复
"""
import httpx
import json
import logging
from typing import Dict, Any, List, Optional
from openai import OpenAI

from app.core.config import settings
from app.services.base_service import BaseService


class SimpleMCPService(BaseService):
    """
    简化的MCP服务类

    特点：
    1. AI只需要知道一个HTTP地址就能使用所有MCP功能
    2. 自动发现可用工具，无需手动配置
    3. 统一的调用格式，简化集成
    4. 🚀 自动健康检查和重连机制
    5. 🚀 智能缓存和故障恢复
    """

    def __init__(self, mcp_base_url: str = "http://localhost:8000/api/ai/mcp"):
        super().__init__()
        self.mcp_base_url = mcp_base_url
        self.client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )
        self._available_tools = None

        # 🚀 延迟导入健康检查服务，避免循环导入
        self._health_service = None

    def _get_health_service(self):
        """延迟获取健康检查服务"""
        if self._health_service is None:
            try:
                from app.services.mcp_health_service import mcp_health_service
                self._health_service = mcp_health_service
            except ImportError:
                self.logger.warning("健康检查服务不可用，使用基础模式")
                self._health_service = False
        return self._health_service

    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """
        获取所有可用的MCP工具

        🚀 新增：使用健康检查服务获取工具列表

        :return: 工具列表
        """
        try:
            # 尝试使用健康检查服务
            health_service = self._get_health_service()
            if health_service:
                result = await health_service.get_tools_with_health_check()
                if result.get("success"):
                    self._available_tools = result.get("tools", [])
                    self.logger.info(f"✅ 获取工具列表成功 (来源: {result.get('source', 'unknown')})")
                    return self._available_tools
                else:
                    self.logger.warning(f"健康检查服务获取工具失败: {result.get('error')}")

            # 回退到基础模式
            self.logger.info("🔄 使用基础模式获取工具列表")
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.mcp_base_url}/tools")
                response.raise_for_status()

                data = response.json()
                if data.get("success"):
                    self._available_tools = data.get("tools", [])
                    return self._available_tools
                else:
                    self.logger.error(f"获取工具列表失败: {data.get('error')}")
                    return []

        except Exception as e:
            self.logger.error(f"获取MCP工具列表失败: {str(e)}")
            return []
    
    async def call_mcp_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具

        🚀 新增：使用健康检查服务进行重试调用

        :param tool_name: 工具名称
        :param parameters: 工具参数
        :return: 调用结果
        """
        try:
            # 尝试使用健康检查服务
            health_service = self._get_health_service()
            if health_service:
                result = await health_service.call_tool_with_retry(tool_name, parameters)
                if result.get("success", True):  # 如果没有success字段，默认认为成功
                    self.logger.info(f"✅ 工具调用成功: {tool_name}")
                    return result
                else:
                    self.logger.warning(f"健康检查服务调用工具失败: {result.get('error')}")

            # 回退到基础模式
            self.logger.info(f"🔄 使用基础模式调用工具: {tool_name}")
            async with httpx.AsyncClient() as client:
                payload = {
                    "tool_name": tool_name,
                    "parameters": parameters
                }

                response = await client.post(
                    f"{self.mcp_base_url}/call",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                response.raise_for_status()

                return response.json()

        except Exception as e:
            self.logger.error(f"调用MCP工具失败: {tool_name}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name,
                "parameters": parameters
            }
    
    async def chat_with_mcp(
        self, 
        message: str, 
        conversation_history: List[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        与AI对话，支持MCP工具调用（通过HTTP接口）
        
        :param message: 用户消息
        :param conversation_history: 对话历史
        :return: AI响应和工具调用结果
        """
        try:
            # 获取可用工具
            if not self._available_tools:
                await self.get_available_tools()
            
            # 构建OpenAI工具格式
            openai_tools = []
            for tool in self._available_tools:
                openai_tool = {
                    "type": "function",
                    "function": {
                        "name": tool["name"],
                        "description": tool["description"],
                        "parameters": {
                            "type": "object",
                            "properties": tool["parameters"],
                            "required": [
                                param_name for param_name, param_info in tool["parameters"].items()
                                if param_info.get("required", False)
                            ]
                        }
                    }
                }
                openai_tools.append(openai_tool)
            
            # 构建消息历史
            messages = []
            
            # 🚀 自动生成系统提示词
            system_prompt = await self._generate_system_prompt()

            messages.append({"role": "system", "content": system_prompt})
            
            # 添加对话历史
            if conversation_history:
                messages.extend(conversation_history)
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": message})
            
            self.logger.info(f"发送消息到AI: {message}")
            
            # 调用AI模型
            response = self.client.chat.completions.create(
                model=settings.DEEPSEEK_MODEL,
                messages=messages,
                tools=openai_tools,
                tool_choice="auto",
                temperature=0.7
            )
            
            assistant_message = response.choices[0].message
            tool_calls = assistant_message.tool_calls
            
            result = {
                "message": assistant_message.content,
                "tool_calls": [],
                "tool_results": []
            }
            
            # 处理工具调用
            if tool_calls:
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    self.logger.info(f"调用MCP工具: {function_name}, 参数: {function_args}")
                    
                    # 通过HTTP调用MCP工具
                    tool_result = await self.call_mcp_tool(function_name, function_args)
                    
                    result["tool_calls"].append({
                        "function": function_name,
                        "arguments": function_args
                    })
                    result["tool_results"].append(tool_result)
                
                # 如果有工具调用，让AI总结结果
                if result["tool_results"]:
                    summary_messages = messages + [
                        {"role": "assistant", "content": assistant_message.content},
                        {"role": "user", "content": f"工具调用结果: {json.dumps(result['tool_results'], ensure_ascii=False)}，请总结一下结果。"}
                    ]
                    
                    summary_response = self.client.chat.completions.create(
                        model=settings.DEEPSEEK_MODEL,
                        messages=summary_messages,
                        temperature=0.7
                    )
                    
                    result["message"] = summary_response.choices[0].message.content
            
            self.logger.info(f"AI响应完成")
            return result
            
        except Exception as e:
            self.logger.error(f"AI对话失败: {str(e)}")
            raise
    
    async def simple_chat(self, message: str) -> str:
        """
        简单对话，不使用工具
        
        :param message: 用户消息
        :return: AI回复
        """
        try:
            response = self.client.chat.completions.create(
                model=settings.DEEPSEEK_MODEL,
                messages=[
                    {"role": "system", "content": "你是一个友好的助手，请用中文回复。"},
                    {"role": "user", "content": message}
                ],
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"简单对话失败: {str(e)}")
            raise

    async def _generate_system_prompt(self) -> str:
        """
        🚀 自动生成系统提示词

        通过HTTP接口获取可用工具并生成系统提示词

        :return: 系统提示词
        """
        try:
            # 获取可用工具
            if not self._available_tools:
                await self.get_available_tools()

            if not self._available_tools:
                return "你是一个智能助手，可以帮助用户完成各种任务。请用中文回复用户。"

            # 按模块分组工具
            modules = {}
            for tool in self._available_tools:
                module_name = tool.get("module", "").split('.')[-1] if tool.get("module") else "unknown"
                if module_name not in modules:
                    modules[module_name] = []

                modules[module_name].append({
                    "name": tool["name"],
                    "description": tool["description"]
                })

            # 构建系统提示词
            prompt_parts = [
                "你是一个智能业务助手，可以帮助用户完成各种任务。",
                "",
                "🔧 可用工具分类："
            ]

            # 🚀 使用HTTP接口获取模块元数据（如果可能的话）
            # 这里简化处理，直接使用工具信息构建提示词
            for module_name, tools in modules.items():
                if module_name == "unknown":
                    continue

                # 根据模块名推断图标和描述
                module_info = self._get_module_info_by_name(module_name)
                module_desc = f"{module_info['icon']} {module_info['name']}"
                prompt_parts.append(f"\n{module_desc}：")
                prompt_parts.append(f"   📝 {module_info['description']}")

                for tool in tools:
                    prompt_parts.append(f"   • {tool['name']} - {tool['description']}")

            prompt_parts.extend([
                "",
                "💡 使用指南：",
                "- 当用户需要相关功能时，请主动调用相应的工具",
                "- 根据工具返回的结果为用户提供清晰的解释",
                "- 如果工具调用失败，请友好地告知用户并建议替代方案",
                "- 请始终用中文与用户交流",
                "",
                f"📊 当前共有 {len(self._available_tools)} 个可用工具为您服务。"
            ])

            return "\n".join(prompt_parts)

        except Exception as e:
            self.logger.error(f"生成系统提示词失败: {str(e)}")
            # 返回默认提示词
            return "你是一个智能助手，可以帮助用户完成各种任务。当用户需要特定功能时，请主动调用相应的工具。请用中文回复用户。"

    def _get_module_info_by_name(self, module_name: str) -> Dict[str, str]:
        """
        根据模块名获取模块信息

        :param module_name: 模块名
        :return: 模块信息
        """
        # 预定义的模块信息映射
        module_info_map = {
            "user_tools": {
                "name": "用户管理",
                "icon": "👤",
                "description": "管理用户信息，包括生成、验证、批量处理等功能"
            },
            "certificate_tools": {
                "name": "证书管理",
                "icon": "📜",
                "description": "管理数字证书申请、查询、批量处理等功能"
            },
            "data_generator": {
                "name": "数据生成",
                "icon": "🎲",
                "description": "生成各种测试数据，包括手机号、身份证、银行卡、邮箱、用户档案等"
            },
            "custom_tools": {
                "name": "实用工具",
                "icon": "⚙️",
                "description": "提供各种实用功能，包括密码生成、JSON格式化、文本分析、时间计算、颜色生成等"
            }
        }

        return module_info_map.get(module_name, {
            "name": module_name.replace('_', ' ').title(),
            "icon": "📁",
            "description": f"{module_name} 模块"
        })
    
    def get_mcp_info(self) -> Dict[str, Any]:
        """
        获取MCP服务信息
        
        :return: MCP服务信息
        """
        return {
            "mcp_base_url": self.mcp_base_url,
            "tools_endpoint": f"{self.mcp_base_url}/tools",
            "call_endpoint": f"{self.mcp_base_url}/call",
            "description": "通过HTTP接口使用MCP工具，只需要配置一个地址即可",
            "usage": {
                "get_tools": f"GET {self.mcp_base_url}/tools",
                "call_tool": f"POST {self.mcp_base_url}/call",
                "payload_format": {
                    "tool_name": "工具名称",
                    "parameters": {"param1": "value1"}
                }
            }
        }
