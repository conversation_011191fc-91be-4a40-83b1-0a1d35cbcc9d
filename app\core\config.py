"""
应用配置设置
"""
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """应用配置类"""
    
    # 项目基本信息
    PROJECT_NAME: str = "FastMCP"
    PROJECT_DESCRIPTION: str = "FastAPI MCP Server with Business Logic Separation"
    VERSION: str = "1.0.0"
    
    # MCP配置
    MCP_NAME: str = "FastMCP API Server"
    MCP_DESCRIPTION: str = "Professional MCP server with separated business logic"
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./test.db"
    
    # 外部API配置
    EXTERNAL_API_BASE_URL: str = "http://sdk.testk8s.tsign.cn"

    # AI模型配置
#     DEEPSEEK_API_KEY: str = "***********************************"
#     DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
#     DEEPSEEK_MODEL: str = "deepseek-chat"
    # 阿里百炼配置
    DEEPSEEK_API_KEY: str = "***********************************"
    DEEPSEEK_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    DEEPSEEK_MODEL: str = "qwen-turbo"

    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"

# 创建全局配置实例
settings = Settings()
