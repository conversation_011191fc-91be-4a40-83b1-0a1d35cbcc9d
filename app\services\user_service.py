"""
用户相关业务服务
"""
from typing import Dict, Any
import random
from app.services.base_service import BaseService

class UserService(BaseService):
    """用户服务类，处理用户相关的业务逻辑"""
    
    async def get_user_or_enterprise_info(self, env: int) -> Dict[str, Any]:
        """
        获取用户或企业信息
        
        :param env: 环境类型 1-测试环境 2-模拟环境
        :return: 用户信息
        """
        try:
            url = f"{self.base_url}/random/get"
            data = {
                "env": "模拟环境" if env == 1 else "测试环境",
                "mock": True,
                "total": 1
            }
            
            response = await self.make_request("POST", url, json_data=data)
            
            if "accountList" not in response or not response["accountList"]:
                raise Exception("未获取到有效的账户数据")
            
            account_data = response["accountList"][0]
            
            # 数据转换和格式化
            result = {
                "姓名": account_data.get("name", ""),
                "手机号": account_data.get("phone", ""),
                "身份证号": account_data.get("idNo", ""),
                "企业名称": account_data.get("orgName", ""),
                "社会编码": account_data.get("orgCode", ""),
                "银行卡号": account_data.get("bankCard", "")
            }
            
            self.logger.info(f"成功获取用户信息: {result['姓名']}")
            return result
            
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {str(e)}")
            raise
    
    async def generate_mock_user(self, user_type: str) -> Dict[str, Any]:
        """
        生成模拟用户数据
        
        :param user_type: 用户类型 personal/enterprise
        :return: 模拟用户数据
        """
        try:
            if user_type == "personal":
                mock_data = {
                    "姓名": f"测试用户{random.randint(1000, 9999)}",
                    "手机号": f"138{random.randint(********, ********)}",
                    "身份证号": f"********{random.randint(80, 99)}0101{random.randint(1000, 9999)}",
                    "企业名称": "",
                    "社会编码": "",
                    "银行卡号": f"6222{random.randint(********0000, ********9999)}"
                }
            else:  # enterprise
                mock_data = {
                    "姓名": f"企业联系人{random.randint(100, 999)}",
                    "手机号": f"139{random.randint(********, ********)}",
                    "身份证号": f"********{random.randint(70, 89)}0101{random.randint(1000, 9999)}",
                    "企业名称": f"测试企业{random.randint(100, 999)}有限公司",
                    "社会编码": f"91110000{random.randint(********0, ********9)}",
                    "银行卡号": f"6228{random.randint(********0000, ********9999)}"
                }
            
            self.logger.info(f"生成{user_type}类型模拟用户数据")
            return {
                "status": "success",
                "data": mock_data,
                "message": "模拟数据生成成功"
            }
            
        except Exception as e:
            self.logger.error(f"生成模拟用户数据失败: {str(e)}")
            raise
