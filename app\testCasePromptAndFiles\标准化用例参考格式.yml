- config:
    name: 搜索关键字
    variables:
      appId: ${ENV(appid1)}
      fileId1: 1eb4f0c926a240b781d862dd839bac6b


- test:
    name: 搜索关键字-fileId不存在
    api: api/files/keyword-positions.yml
    variables:
      fileId: "123"
      json:
        {
          "keywords": ["盖章"]
        }
    validate:
      - eq: ["content.code", 1451001]
      - eq: ["content.message", "查询文档信息失败,合同不存在"]

- test:
    name: 搜索关键字-keywords为空
    api: api/files/keyword-positions.yml
    variables:
      fileId: $fileId1
      json:
        {
          "keywords": []
        }
    validate:
      - eq: ["content.code", 1451000]
      - eq: ["content.message", "参数错误: 未指定关键字"]

- test:
    name: 搜索关键字-keywords在文档中找不到
    api: api/files/keyword-positions.yml
    variables:
      fileId: $fileId1
      json:
        {
          "keywords": ["𧝁"]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.keywordPositions.0.searchResult", false]
      - eq: ["content.data.keywordPositions.0.positions", []]

- test:
    name: 搜索关键字-keywords在文档存在
    api: api/files/keyword-positions.yml
    variables:
      fileId: $fileId1
      json:
        {
          "keywords": ["盖章"]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.keywordPositions.0.searchResult", true]
      - ne: ["content.data.keywordPositions.0.positions", []]