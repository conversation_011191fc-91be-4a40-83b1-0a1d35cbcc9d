请帮我设计接口测试用例，要求如下：
1. 参数定义规则：
   a) 明确识别条件必填参数，并在用例中创建相应的场景。
   b) 验证某个必填参数时，确保其他必填参数不被省略。
   c) 仅使用接口文档中明确列出的参数，不添加未说明的参数。
   d) 非必填参数不需要进行必填校验。
   e) 不允许假设、推理或猜测未明确标注的必填参数。
   f) 递归读取所有层的入参，确保嵌套关系的完整性，特别注意嵌套参数的必填性。

2. 测试用例设计原则：
   a) 每个用例仅测试一个参数的缺失场景。
   b) 其他必填参数必须正确提供。
   c) 仅包含必要的参数，非必填参数可以省略。
   d) 同类参数在不同用例中保持一致的值。

3. 用例格式参考：
   参考用例格式.yml，不允许分开写。

4. 测试数据规范：
   a) 使用有意义的测试数据。
   b) 枚举值使用实际可用的值。
   c) 错误提示统一使用"参数错误: "作为前缀。
   d) 条件类错误需说明触发条件。

5. 用例覆盖范围：
   a) 必须包含一个正向用例作为基准。
   b) 覆盖所有基础必填参数缺失场景。
   c) 覆盖所有嵌套必填参数缺失场景。
   d) 覆盖所有条件必填参数缺失场景
