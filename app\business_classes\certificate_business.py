"""
证书CRUD业务类
=============

基于您的certApis.py文件，提供完整的证书增删改查功能
只需要定义这个类，系统自动生成MCP工具！
"""
from typing import Dict, Any, List, Optional
from app.business_classes import BusinessBase
import requests
import logging


class CertificateBusiness(BusinessBase):
    """证书CRUD管理业务类"""

    class Meta:
        name = "证书管理"
        icon = "📜"
        description = "提供完整的证书增删改查功能，包括创建、查询、更新、吊销证书等操作"
        category = "core"
        ai_prompt_hint = "当用户需要证书相关操作时，如创建证书、查询证书详情、更新证书信息、吊销证书等，请使用此类的方法"

    def __init__(self):
        super().__init__()
        # 环境配置
        self.env_urls = {
            "test": "http://cert-service.testk8s.tsign.cn",
            "sml": "http://cert-service.smlk8s.esign.cn"
        }
        self.current_env = "test"  # 默认测试环境
        self.request_headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-Auth-Mode": "simple",
            "X-Tsign-Open-App-Id": "",
            "X-Tsign-Service-Group": "DEFAULT"
        }
    
    def _post_request(self, url: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送POST请求的内部方法

        :param url: 请求URL
        :param data: 请求数据
        :return: 响应结果
        """
        try:
            base_url = self.env_urls[self.current_env]
            full_url = base_url + url

            response = requests.post(
                full_url,
                json=data,
                headers=self.request_headers,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求失败: {str(e)}")
            return {
                "status": "error",
                "message": f"请求失败: {str(e)}"
            }

    def 设置环境(self, 环境名称: str = "test", app_id: str = "", 服务组: str = "DEFAULT") -> Dict[str, Any]:
        """
        设置证书服务环境和认证信息

        :param 环境名称: 环境名称 test/sml
        :param app_id: 应用ID
        :param 服务组: 服务组名称
        :return: 设置结果
        """
        try:
            # 标准化环境名称
            if "test" in 环境名称.lower() or "测试" in 环境名称:
                self.current_env = "test"
            elif "sml" in 环境名称.lower() or "模拟" in 环境名称 or "pre" in 环境名称.lower():
                self.current_env = "sml"
            else:
                self.current_env = "test"

            # 更新请求头
            self.request_headers.update({
                "X-Tsign-Open-App-Id": app_id,
                "X-Tsign-Service-Group": 服务组
            })

            return {
                "status": "success",
                "data": {
                    "环境": self.current_env,
                    "环境地址": self.env_urls[self.current_env],
                    "应用ID": app_id,
                    "服务组": 服务组
                },
                "message": f"环境设置成功: {self.current_env}"
            }
        except Exception as e:
            self.logger.error(f"设置环境失败: {str(e)}")
            return {
                "status": "error",
                "message": f"设置环境失败: {str(e)}"
            }

    def 创建证书(self, 证书名称: str, 手机号: str, 证件号: str, 算法: str = "SM2",
                证书时长: str = "ONEYEAR", 配置ID: str = "**********") -> Dict[str, Any]:
        """
        创建新的数字证书

        :param 证书名称: 证书持有人姓名
        :param 手机号: 手机号码
        :param 证件号: 身份证号码
        :param 算法: 加密算法，默认SM2
        :param 证书时长: 证书有效期，默认一年
        :param 配置ID: 证书配置ID
        :return: 证书创建结果
        """
        try:
            url = "/openca/rest/cert/createnew"
            data = {
                "certParam": {
                    "algorithm": 算法,
                    "certPolicy": "COMMON",
                    "certTime": 证书时长,
                    "certType": "SINGLE",
                    "configId": 配置ID,
                    "isUkey": False,
                    "issuer": "ZHCA"
                },
                "commonParam": {
                    "address": "天堂软件园",
                    "mobile": 手机号,
                    "phone": 手机号
                },
                "userParam": {
                    "certName": 证书名称,
                    "licenseNumber": 证件号,
                    "licenseType": 19
                }
            }

            response = self._post_request(url, data)

            if response.get("status") == "error":
                return response

            return {
                "status": "success",
                "data": {
                    "证书ID": response.get("certInfoId"),
                    "证书信息": response.get("signCert"),
                    "证书名称": 证书名称,
                    "手机号": 手机号,
                    "证件号": 证件号
                },
                "message": "证书创建成功"
            }

        except Exception as e:
            self.logger.error(f"创建证书失败: {str(e)}")
            return {
                "status": "error",
                "message": f"创建证书失败: {str(e)}"
            }
    
    def 查询证书详情(self, 证书ID: str, 配置ID: str = "**********") -> Dict[str, Any]:
        """
        查询证书详细信息

        :param 证书ID: 证书信息ID
        :param 配置ID: 项目配置ID
        :return: 证书详情
        """
        try:
            url = "/openca/rest/cert/detail"
            data = {
                "certInfoId": 证书ID,
                "projectId": 配置ID
            }

            response = self._post_request(url, data)

            if response.get("status") == "error":
                return response

            cert_info = response.get("certInfo", {})

            return {
                "status": "success",
                "data": {
                    "证书ID": 证书ID,
                    "证书名称": cert_info.get("certname"),
                    "证书状态": cert_info.get("status"),
                    "创建时间": cert_info.get("createTime"),
                    "有效期开始": cert_info.get("validFrom"),
                    "有效期结束": cert_info.get("validTo"),
                    "证书详情": cert_info
                },
                "message": "查询证书详情成功"
            }

        except Exception as e:
            self.logger.error(f"查询证书详情失败: {str(e)}")
            return {
                "status": "error",
                "message": f"查询证书详情失败: {str(e)}"
            }

    def 更新证书(self, 证书ID: str, 证书名称: str, 手机号: str, 证件号: str,
                算法: str = "SM2", 证书时长: str = "ONEYEAR", 配置ID: str = "**********") -> Dict[str, Any]:
        """
        更新证书信息

        :param 证书ID: 证书信息ID
        :param 证书名称: 新的证书持有人姓名
        :param 手机号: 新的手机号码
        :param 证件号: 新的身份证号码
        :param 算法: 加密算法
        :param 证书时长: 证书有效期
        :param 配置ID: 证书配置ID
        :return: 更新结果
        """
        try:
            url = "/openca/rest/cert/update"
            data = {
                "certInfoId": 证书ID,
                "certParam": {
                    "algorithm": 算法,
                    "certPolicy": "COMMON",
                    "certTime": 证书时长,
                    "certType": "SINGLE",
                    "configId": 配置ID,
                    "isUkey": False,
                    "issuer": "ZHCA"
                },
                "commonParam": {
                    "address": "天堂软件园",
                    "mobile": 手机号,
                    "phone": 手机号
                },
                "userParam": {
                    "certName": 证书名称,
                    "licenseNumber": 证件号,
                    "licenseType": 19
                }
            }

            response = self._post_request(url, data)

            if response.get("status") == "error":
                return response

            return {
                "status": "success",
                "data": {
                    "证书ID": 证书ID,
                    "更新后名称": 证书名称,
                    "更新后手机": 手机号,
                    "更新后证件": 证件号,
                    "更新结果": response
                },
                "message": "证书更新成功"
            }

        except Exception as e:
            self.logger.error(f"更新证书失败: {str(e)}")
            return {
                "status": "error",
                "message": f"更新证书失败: {str(e)}"
            }

    def 吊销证书(self, 证书ID: str) -> Dict[str, Any]:
        """
        吊销指定的数字证书

        :param 证书ID: 证书信息ID
        :return: 吊销结果
        """
        try:
            url = "/openca/rest/cert/revoke"
            data = {
                "certInfoId": 证书ID
            }

            response = self._post_request(url, data)

            if response.get("status") == "error":
                return response

            return {
                "status": "success",
                "data": {
                    "证书ID": 证书ID,
                    "吊销结果": response
                },
                "message": "证书吊销成功"
            }

        except Exception as e:
            self.logger.error(f"吊销证书失败: {str(e)}")
            return {
                "status": "error",
                "message": f"吊销证书失败: {str(e)}"
            }

    def 获取测试账号(self) -> Dict[str, Any]:
        """
        获取测试用的账号信息

        :return: 测试账号信息
        """
        try:
            url = "http://sdk.testk8s.tsign.cn/random/get"
            data = {
                "env": "测试环境",
                "mock": False,
                "total": 1
            }

            response = requests.post(url, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get("accountList"):
                account = result["accountList"][0]
                return {
                    "status": "success",
                    "data": {
                        "姓名": account.get("name"),
                        "手机号": account.get("phone"),
                        "身份证号": account.get("idNo"),
                        "完整信息": account
                    },
                    "message": "获取测试账号成功"
                }
            else:
                return {
                    "status": "error",
                    "message": "未获取到测试账号"
                }

        except Exception as e:
            self.logger.error(f"获取测试账号失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取测试账号失败: {str(e)}"
            }

    def 批量创建证书(self, 数量: int = 3) -> Dict[str, Any]:
        """
        批量创建多个证书（使用随机测试账号）

        :param 数量: 创建证书的数量
        :return: 批量创建结果
        """
        try:
            certificates = []
            failed_count = 0

            for i in range(数量):
                # 获取测试账号
                account_result = self.获取测试账号()
                if account_result.get("status") == "success":
                    account_data = account_result["data"]

                    # 创建证书
                    cert_result = self.创建证书(
                        证书名称=account_data["姓名"],
                        手机号=account_data["手机号"],
                        证件号=account_data["身份证号"]
                    )

                    if cert_result.get("status") == "success":
                        certificates.append(cert_result["data"])
                    else:
                        failed_count += 1
                        self.logger.warning(f"第{i+1}个证书创建失败: {cert_result.get('message')}")
                else:
                    failed_count += 1
                    self.logger.warning(f"第{i+1}个账号获取失败")

            return {
                "status": "success",
                "data": {
                    "证书列表": certificates,
                    "成功数量": len(certificates),
                    "失败数量": failed_count,
                    "总数量": 数量
                },
                "message": f"批量创建完成，成功{len(certificates)}个，失败{failed_count}个"
            }

        except Exception as e:
            self.logger.error(f"批量创建证书失败: {str(e)}")
            return {
                "status": "error",
                "message": f"批量创建证书失败: {str(e)}"
            }

    def 证书完整流程测试(self) -> Dict[str, Any]:
        """
        执行完整的证书CRUD流程测试

        :return: 测试结果
        """
        try:
            test_results = []

            # 1. 获取测试账号
            account_result = self.获取测试账号()
            if account_result.get("status") != "success":
                return {
                    "status": "error",
                    "message": "获取测试账号失败"
                }

            account_data = account_result["data"]
            test_results.append(f"✅ 获取测试账号: {account_data['姓名']}")

            # 2. 创建证书
            create_result = self.创建证书(
                证书名称=account_data["姓名"],
                手机号=account_data["手机号"],
                证件号=account_data["身份证号"]
            )

            if create_result.get("status") != "success":
                return {
                    "status": "error",
                    "message": f"创建证书失败: {create_result.get('message')}"
                }

            cert_id = create_result["data"]["证书ID"]
            test_results.append(f"✅ 创建证书成功: {cert_id}")

            # 3. 查询证书详情
            detail_result = self.查询证书详情(cert_id)
            if detail_result.get("status") == "success":
                test_results.append(f"✅ 查询证书详情成功")
            else:
                test_results.append(f"❌ 查询证书详情失败: {detail_result.get('message')}")

            # 4. 获取新的测试账号用于更新
            new_account_result = self.获取测试账号()
            if new_account_result.get("status") == "success":
                new_account_data = new_account_result["data"]

                # 5. 更新证书
                update_result = self.更新证书(
                    证书ID=cert_id,
                    证书名称=new_account_data["姓名"],
                    手机号=new_account_data["手机号"],
                    证件号=new_account_data["身份证号"]
                )

                if update_result.get("status") == "success":
                    test_results.append(f"✅ 更新证书成功: {new_account_data['姓名']}")
                else:
                    test_results.append(f"❌ 更新证书失败: {update_result.get('message')}")

            # 6. 吊销证书
            revoke_result = self.吊销证书(cert_id)
            if revoke_result.get("status") == "success":
                test_results.append(f"✅ 吊销证书成功")
            else:
                test_results.append(f"❌ 吊销证书失败: {revoke_result.get('message')}")

            return {
                "status": "success",
                "data": {
                    "测试步骤": test_results,
                    "证书ID": cert_id,
                    "原始账号": account_data,
                    "更新账号": new_account_data if 'new_account_data' in locals() else None
                },
                "message": "证书完整流程测试完成"
            }

        except Exception as e:
            self.logger.error(f"证书流程测试失败: {str(e)}")
            return {
                "status": "error",
                "message": f"证书流程测试失败: {str(e)}"
            }
