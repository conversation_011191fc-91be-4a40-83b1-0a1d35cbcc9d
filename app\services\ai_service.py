"""
AI服务 - 集成DeepSeek模型和MCP工具调用
"""
import json
import logging
import inspect
from typing import Dict, Any, List, Optional
from openai import OpenAI

from app.core.config import settings
from app.services.base_service import BaseService
from app.business_classes import get_all_business_methods, get_business_methods_with_metadata

class AIService(BaseService):
    """AI服务类，处理AI对话和MCP工具调用"""
    
    def __init__(self):
        super().__init__()
        self.client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )

        # 🚀 自动生成MCP工具定义 - 无需手动配置！
        self.available_tools = self._auto_generate_tools()

    def _auto_generate_tools(self) -> List[Dict[str, Any]]:
        """
        🚀 自动从业务层生成OpenAI工具定义

        🎯 实现目标：
        1. 自动发现business文件夹中的所有函数
        2. 自动解析函数签名和类型注解
        3. 自动生成OpenAI工具格式
        4. 新增函数自动生效，无需手动配置

        :return: OpenAI工具定义列表
        """
        tools = []

        # 🔍 自动发现业务类中的所有方法
        business_methods = get_all_business_methods()

        for name, instance, method in business_methods:
            # 跳过私有函数
            if name.startswith('_'):
                continue

            # 获取方法签名
            sig = inspect.signature(method)

            # 构建OpenAI工具格式
            tool_def = {
                "type": "function",
                "function": {
                    "name": name,
                    "description": self._clean_docstring(method.__doc__) or f"调用{name}方法",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }

            # 自动解析参数
            for param_name, param in sig.parameters.items():
                param_info = self._parse_parameter(param)
                tool_def["function"]["parameters"]["properties"][param_name] = param_info

                # 如果参数没有默认值，则为必需参数
                if param.default == inspect.Parameter.empty:
                    tool_def["function"]["parameters"]["required"].append(param_name)

            tools.append(tool_def)
            self.logger.info(f"🔧 自动生成业务工具: {name}")

        self.logger.info(f"🎉 自动生成了 {len(tools)} 个业务MCP工具")
        return tools

    def _clean_docstring(self, docstring: str) -> str:
        """
        清理文档字符串，提取简洁的描述

        :param docstring: 原始文档字符串
        :return: 清理后的描述
        """
        if not docstring:
            return ""

        # 提取第一行作为简短描述
        lines = docstring.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith(':'):
                return line

        return docstring.strip()

    def _parse_parameter(self, param: inspect.Parameter) -> Dict[str, Any]:
        """
        解析函数参数，生成OpenAI参数定义

        :param param: 函数参数
        :return: OpenAI参数定义
        """
        param_info = {
            "type": "string",  # 默认类型
            "description": f"参数 {param.name}"
        }

        # 根据类型注解设置参数类型
        if param.annotation != inspect.Parameter.empty:
            if param.annotation == int:
                param_info["type"] = "integer"
            elif param.annotation == float:
                param_info["type"] = "number"
            elif param.annotation == bool:
                param_info["type"] = "boolean"
            elif param.annotation == list or str(param.annotation).startswith('typing.List'):
                param_info["type"] = "array"
            elif param.annotation == dict or str(param.annotation).startswith('typing.Dict'):
                param_info["type"] = "object"

        # 设置默认值
        if param.default != inspect.Parameter.empty:
            param_info["default"] = param.default

        return param_info

    def _generate_system_prompt(self) -> str:
        """
        🚀 自动生成系统提示词

        根据可用的业务工具和模块元数据自动生成系统提示词，无需手动维护

        :return: 系统提示词
        """
        try:
            # 🔍 获取业务方法和类元数据
            business_methods, class_metadata = get_business_methods_with_metadata()

            # 按业务类分组工具
            classes = {}
            for name, instance, method in business_methods:
                if name.startswith('_'):
                    continue

                class_name = instance.__class__.__name__
                if class_name not in classes:
                    classes[class_name] = []

                # 提取方法描述
                description = self._clean_docstring(method.__doc__) or name
                classes[class_name].append({
                    "name": name,
                    "description": description
                })

            # 构建系统提示词
            prompt_parts = [
                "你是一个智能业务助手，可以帮助用户完成各种任务。",
                "",
                "🔧 可用工具分类："
            ]

            # 🚀 使用自动读取的业务类元数据
            for class_name, tools in classes.items():
                if class_name in class_metadata:
                    meta = class_metadata[class_name]
                    class_desc = f"{meta['icon']} {meta['name']}"
                    prompt_parts.append(f"\n{class_desc}：")
                    prompt_parts.append(f"   📝 {meta['description']}")

                    for tool in tools:
                        prompt_parts.append(f"   • {tool['name']} - {tool['description']}")
                else:
                    # 兜底处理
                    prompt_parts.append(f"\n📁 {class_name}：")
                    for tool in tools:
                        prompt_parts.append(f"   • {tool['name']} - {tool['description']}")

            # 添加AI使用指南（基于业务类元数据的提示）
            prompt_parts.extend([
                "",
                "💡 AI使用指南："
            ])

            for class_name, meta in class_metadata.items():
                if class_name in classes:  # 只添加有方法的类
                    prompt_parts.append(f"- {meta['ai_prompt_hint']}")

            prompt_parts.extend([
                "- 根据工具返回的结果为用户提供清晰的解释",
                "- 如果工具调用失败，请友好地告知用户并建议替代方案",
                "- 请始终用中文与用户交流",
                "",
                f"📊 当前共有 {len([name for name, _, _ in business_methods if not name.startswith('_')])} 个可用工具为您服务。"
            ])

            return "\n".join(prompt_parts)

        except Exception as e:
            self.logger.error(f"生成系统提示词失败: {str(e)}")
            # 返回默认提示词
            return "你是一个智能助手，可以帮助用户完成各种任务。当用户需要特定功能时，请主动调用相应的工具。请用中文回复用户。"
    
    async def chat_with_tools(
        self, 
        message: str, 
        conversation_history: List[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        与AI对话，支持工具调用
        
        :param message: 用户消息
        :param conversation_history: 对话历史
        :return: AI响应和工具调用结果
        """
        try:
            # 构建消息历史
            messages = []
            
            # 🚀 自动生成系统提示词
            system_prompt = self._generate_system_prompt()

            messages.append({"role": "system", "content": system_prompt})
            
            # 添加对话历史
            if conversation_history:
                messages.extend(conversation_history)
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": message})
            
            self.logger.info(f"发送消息到DeepSeek: {message}")
            
            # 调用AI模型
            response = self.client.chat.completions.create(
                model=settings.DEEPSEEK_MODEL,
                messages=messages,
                tools=self.available_tools,
                tool_choice="auto",
                temperature=0.7
            )
            
            assistant_message = response.choices[0].message
            tool_calls = assistant_message.tool_calls
            
            result = {
                "message": assistant_message.content,
                "tool_calls": [],
                "tool_results": []
            }
            
            # 处理工具调用
            if tool_calls:
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    self.logger.info(f"调用工具: {function_name}, 参数: {function_args}")
                    
                    # 调用对应的MCP工具
                    tool_result = await self._call_mcp_tool(function_name, function_args)
                    
                    result["tool_calls"].append({
                        "function": function_name,
                        "arguments": function_args
                    })
                    result["tool_results"].append(tool_result)
                
                # 如果有工具调用，让AI总结结果
                if result["tool_results"]:
                    summary_messages = messages + [
                        {"role": "assistant", "content": assistant_message.content},
                        {"role": "user", "content": f"工具调用结果: {json.dumps(result['tool_results'], ensure_ascii=False)}，请总结一下结果。"}
                    ]
                    
                    summary_response = self.client.chat.completions.create(
                        model=settings.DEEPSEEK_MODEL,
                        messages=summary_messages,
                        temperature=0.7
                    )
                    
                    result["message"] = summary_response.choices[0].message.content
            
            self.logger.info(f"AI响应完成")
            return result
            
        except Exception as e:
            self.logger.error(f"AI对话失败: {str(e)}")
            raise
    
    async def _call_mcp_tool(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        🚀 调用业务层MCP工具

        :param function_name: 函数名
        :param arguments: 参数
        :return: 工具调用结果
        """
        try:
            # 🔍 从业务类查找方法
            business_methods = get_all_business_methods()
            method_dict = {name: (instance, method) for name, instance, method in business_methods}

            if function_name in method_dict:
                instance, method = method_dict[function_name]

                # 🔧 调用业务方法
                result = method(**arguments)

                self.logger.info(f"✅ 成功调用业务方法: {function_name}")
                return result
            else:
                self.logger.warning(f"❌ 未找到业务方法: {function_name}")
                return {
                    "status": "error",
                    "message": f"未知的工具: {function_name}",
                    "available_tools": list(method_dict.keys())
                }

        except Exception as e:
            self.logger.error(f"调用业务工具失败: {function_name}, 错误: {str(e)}")
            return {
                "status": "error",
                "message": f"工具调用失败: {str(e)}",
                "function_name": function_name,
                "arguments": arguments
            }
    
    async def simple_chat(self, message: str) -> str:
        """
        简单对话，不使用工具
        
        :param message: 用户消息
        :return: AI回复
        """
        try:
            response = self.client.chat.completions.create(
                model=settings.DEEPSEEK_MODEL,
                messages=[
                    {"role": "system", "content": "你是一个友好的助手，请用中文回复。"},
                    {"role": "user", "content": message}
                ],
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"简单对话失败: {str(e)}")
            raise
