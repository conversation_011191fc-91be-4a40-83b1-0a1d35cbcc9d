# 🚀 集测用例生成服务使用指南

## 📋 背景说明

原来在Trae中生成集测用例需要：
1. ❌ 输入长篇提示词
2. ❌ 引用标准格式用例文件
3. ❌ 提供接口文档
4. ❌ 提供标准化入参

现在只需要：
1. ✅ 配置MCP服务地址
2. ✅ 提供接口文档
3. ✅ 提供标准化入参

## 🔧 配置方法

### 1. 在Trae中配置MCP服务

```json
{
  "mcp_server_url": "http://localhost:8000/api/ai/mcp",
  "description": "集测用例生成服务"
}
```

### 2. 验证配置

在Trae中输入：
```
请帮我获取可用的集测工具
```

应该能看到以下工具：
- `生成枚举集测用例` - 一键生成HttpRunner格式的枚举测试用例
- `获取支持的枚举类型` - 查看支持的枚举类型
- `生成用例模板` - 生成标准用例模板

## 🚀 使用方法

### 基础用法

在Trae中直接输入：

```
请帮我生成枚举集测用例，接口文档如下：

**接口地址** `/v1/certs/create-cert-task`
**请求方式** `POST`
**请求参数**

| 参数名称 | 说明 | 参数类型 | 是否必须 | 类型 | schema |
|----------|------|----------|----------|------|--------|
| param | param | body | true | CertCreateTaskRequest | CertCreateTaskRequest |

**schema属性说明**

**CertCreateTaskRequest**

| 参数名称 | 说明 | 参数类型 | 是否必须 | 类型 | schema |
|----------|------|----------|----------|------|--------|
| bizType | 业务类型，可选值：TCLOUD、ESIGN | body | true | string | |
| operatorType | 操作类型，1、申请 2、撤销 | body | true | int | |
| userType | 用户类型，1、个人 2、企业 | body | false | int | |

标准化入参：
{'bizType': 'TCLOUD', 'operatorType': 1, 'userType': 2}
```

### 高级用法

```
请生成证书申请接口的枚举集测用例，要求包含所有枚举参数的测试场景。

接口文档：[粘贴完整接口文档]

标准化入参：{'bizType': 'TCLOUD', 'operatorType': 'APPLY', 'projectId': '**********', 'notifyUrl': 'http://libaohui.com.cn/callback/ding', 'redirectUrl': 'https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai', 'phone': '19888644846', 'applyConfigModel': {'certNum': 1}, 'applyCommonModel': {}, 'applyUserModel': {'certName': 'esigntest苍诚经营的个体工商户', 'licenseNumber': '91000000BL0X3BDKXJ', 'licenseType': 1, 'userType': 2}}
```

## 📊 输出结果

系统会自动生成：

### 1. HttpRunner格式的yml文件
```yaml
config:
  name: 枚举参数集测用例
  base_url: ${ENV(base_url)}
  variables:
    common_headers:
      Content-Type: application/json

teststeps:
- name: 测试bizType枚举值: TCLOUD
  request:
    url: /v1/certs/create-cert-task
    method: POST
    headers: $common_headers
    json:
      bizType: TCLOUD
      operatorType: 1
      userType: 2
  validate:
  - eq: [status_code, 200]

- name: 测试bizType枚举值: ESIGN
  request:
    url: /v1/certs/create-cert-task
    method: POST
    headers: $common_headers
    json:
      bizType: ESIGN
      operatorType: 1
      userType: 2
  validate:
  - eq: [status_code, 200]
```

### 2. 详细分析报告
- 发现的枚举参数数量
- 生成的测试用例数量
- 每个枚举参数的详细信息
- 使用指南

## 🔍 支持的枚举类型

### 1. 整数枚举
```
操作类型，1、申请 2、撤销 3、暂停
用户类型：1、个人 2、企业
```

### 2. 字符串枚举
```
业务类型，可选值：TCLOUD、ESIGN、HYBRID
状态：ACTIVE、INACTIVE、PENDING
```

### 3. 可选值格式
```
可选值：true、false
可选：是、否
取值范围：1-10
```

### 4. 嵌套参数枚举
```
applyUserModel.licenseType: 1、营业执照 2、组织机构代码证
applyConfigModel.certType: RSA、SM2
```

## 🛠️ 高级功能

### 1. 查看支持的枚举类型
```
请告诉我支持哪些枚举类型
```

### 2. 生成用例模板
```
请生成一个HttpRunner用例模板
```

### 3. 自定义接口信息
```
请生成集测用例，接口地址是 /api/test，请求方式是 PUT
```

## 📈 优势对比

| 功能 | 原来的方式 | 现在的方式 |
|------|------------|------------|
| 提示词 | ❌ 需要输入长篇提示词 | ✅ 内置标准提示词 |
| 用例模板 | ❌ 需要引用标准文件 | ✅ 内置HttpRunner模板 |
| 参数提取 | ❌ 手动分析 | ✅ 自动递归提取 |
| 用例生成 | ❌ 手动编写 | ✅ 自动生成yml格式 |
| 使用复杂度 | ❌ 高，需要多个步骤 | ✅ 低，一步完成 |
| 维护成本 | ❌ 高，分散在各处 | ✅ 低，集中管理 |

## 🔧 故障排除

### 1. 枚举参数提取不全
**原因**: 接口文档格式不标准
**解决**: 确保枚举值在参数说明中明确列出

### 2. 生成的用例格式错误
**原因**: 标准化入参格式不正确
**解决**: 确保入参是有效的JSON格式

### 3. 无法识别嵌套参数
**原因**: schema定义不完整
**解决**: 提供完整的schema属性说明

## 📞 技术支持

### API接口
```bash
# 直接调用API
curl -X POST http://localhost:8000/api/ai/mcp/call \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "生成枚举集测用例",
    "parameters": {
      "接口文档": "...",
      "标准化入参": "..."
    }
  }'
```

### 健康检查
```bash
curl http://localhost:8000/api/ai/mcp/health
```

## 🚀 最佳实践

### 1. 接口文档规范
- 使用标准的Markdown表格格式
- 在参数说明中明确列出所有枚举值
- 提供完整的schema定义

### 2. 标准化入参
- 使用有效的JSON格式
- 包含所有必需参数
- 提供合理的默认值

### 3. 用例优化
- 根据业务需求调整验证规则
- 添加必要的环境变量
- 完善错误场景测试

---

**🎯 核心价值**: 将复杂的集测用例生成流程简化为一步操作，大幅提升测试效率！
