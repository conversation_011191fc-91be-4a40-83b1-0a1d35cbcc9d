"""
完整系统测试
===========

验证整个MCP业务类自动化系统是否正常工作
"""
import requests
import json

def test_complete_system():
    """测试完整系统功能"""
    print("🚀 完整系统功能测试")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 1. 测试业务工具自动发现
    print("1. 🔍 测试业务工具自动发现...")
    try:
        response = requests.get(f"{base_url}/api/ai/mcp/tools")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                tools = data.get("tools", [])
                print(f"   ✅ 自动发现了 {len(tools)} 个业务工具")
                
                # 按业务类分组
                classes = {}
                for tool in tools:
                    class_name = tool.get("class", "unknown")
                    if class_name not in classes:
                        classes[class_name] = []
                    classes[class_name].append(tool["name"])
                
                for class_name, tool_names in classes.items():
                    print(f"   📁 {class_name}: {len(tool_names)} 个方法")
                    for tool_name in tool_names[:3]:  # 只显示前3个
                        print(f"      - {tool_name}")
                    if len(tool_names) > 3:
                        print(f"      ... 还有 {len(tool_names) - 3} 个方法")
            else:
                print(f"   ❌ 获取工具失败: {data.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False
    
    # 2. 测试各业务类的核心功能
    print("\n2. 🧪 测试各业务类的核心功能...")
    
    test_calls = [
        {
            "name": "用户管理 - 生成模拟用户",
            "tool_name": "生成模拟用户",
            "parameters": {"用户类型": "personal"}
        },
        {
            "name": "数据生成 - 生成手机号",
            "tool_name": "生成手机号",
            "parameters": {"数量": 2, "运营商": "移动"}
        },
        {
            "name": "证书管理 - 获取测试账号",
            "tool_name": "获取测试账号",
            "parameters": {}
        },
        {
            "name": "实用工具 - 生成密码",
            "tool_name": "生成测试密码",
            "parameters": {"数量": 1, "长度": 10}
        }
    ]
    
    for test_call in test_calls:
        print(f"\n   测试: {test_call['name']}")
        try:
            payload = {
                "tool_name": test_call["tool_name"],
                "parameters": test_call["parameters"]
            }
            
            response = requests.post(
                f"{base_url}/api/ai/mcp/call",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    result = data.get("result", {})
                    if result.get("status") == "success":
                        print(f"   ✅ 成功！{result.get('message', '')}")
                        # 显示部分结果数据
                        result_data = result.get("data", {})
                        if "手机号列表" in result_data:
                            print(f"      📱 生成: {result_data['手机号列表']}")
                        elif "密码列表" in result_data:
                            print(f"      🔐 生成: {result_data['密码列表']}")
                        elif "姓名" in result_data:
                            print(f"      👤 用户: {result_data['姓名']}")
                    else:
                        print(f"   ⚠️ 部分成功: {result.get('message', '')}")
                else:
                    print(f"   ❌ 调用失败: {data.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    # 3. 测试证书CRUD完整流程
    print("\n3. 📜 测试证书CRUD完整流程...")
    try:
        # 设置环境
        env_result = requests.post(
            f"{base_url}/api/ai/mcp/call",
            json={
                "tool_name": "设置环境",
                "parameters": {"环境名称": "test", "app_id": "test_app"}
            },
            headers={"Content-Type": "application/json"}
        )
        
        if env_result.status_code == 200 and env_result.json().get("success"):
            print("   ✅ 环境设置成功")
            
            # 执行完整流程测试
            flow_result = requests.post(
                f"{base_url}/api/ai/mcp/call",
                json={
                    "tool_name": "证书完整流程测试",
                    "parameters": {}
                },
                headers={"Content-Type": "application/json"}
            )
            
            if flow_result.status_code == 200:
                data = flow_result.json()
                if data.get("success"):
                    result = data.get("result", {})
                    if result.get("status") == "success":
                        print("   ✅ 证书完整流程测试成功")
                        test_steps = result.get("data", {}).get("测试步骤", [])
                        for step in test_steps[:5]:  # 显示前5个步骤
                            print(f"      {step}")
                        if len(test_steps) > 5:
                            print(f"      ... 还有 {len(test_steps) - 5} 个步骤")
                    else:
                        print(f"   ⚠️ 流程测试部分成功: {result.get('message')}")
                else:
                    print(f"   ❌ 流程测试失败: {data.get('error')}")
            else:
                print(f"   ❌ 流程测试HTTP错误: {flow_result.status_code}")
        else:
            print("   ❌ 环境设置失败，跳过流程测试")
    except Exception as e:
        print(f"   ❌ 证书流程测试失败: {e}")
    
    # 4. 测试AI自动调用（简单测试）
    print("\n4. 🤖 测试AI自动调用...")
    try:
        payload = {
            "message": "生成一个手机号",
            "use_tools": True
        }
        
        response = requests.post(
            f"{base_url}/api/ai/simple-mcp/chat",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("message"):
                print(f"   ✅ AI调用成功")
                print(f"   🤖 AI回复: {data['message'][:80]}...")
                if data.get("tool_calls"):
                    tools_used = [call['function'] for call in data['tool_calls']]
                    print(f"   🔧 AI自动调用了: {tools_used}")
                else:
                    print(f"   ⚠️ AI没有调用工具")
            else:
                print(f"   ❌ AI调用失败")
        else:
            print(f"   ❌ AI调用HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ AI调用测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 完整系统测试完成！")
    
    print("\n🏗️ 系统架构总结:")
    print("   📁 app/business_classes/")
    print("      ├── user_business.py         # 👤 用户管理业务类")
    print("      ├── certificate_business.py  # 📜 证书CRUD业务类")
    print("      ├── data_generator.py        # 🎲 数据生成业务类")
    print("      └── custom_tools.py          # ⚙️ 实用工具业务类")
    
    print("\n✨ 核心特性:")
    print("   🔥 极简定义 - 只需定义业务类")
    print("   ⚡ 自动生成 - 自动生成MCP工具")
    print("   🤖 AI直调 - AI可直接调用业务方法")
    print("   🛡️ 自动异步 - 自动处理异步调用")
    print("   📝 智能提示 - 自动生成系统提示词")
    print("   🌐 统一接口 - 所有方法使用相同API")
    
    print("\n📜 证书CRUD集成:")
    print("   ✅ 完全基于您的certApis.py文件")
    print("   ✅ 保持原有API接口和逻辑")
    print("   ✅ 支持完整的增删改查操作")
    print("   ✅ 提供批量操作和流程测试")
    print("   ✅ AI可以直接调用证书功能")
    
    print("\n🚀 使用方法:")
    print("   1. 在business_classes中定义业务类")
    print("   2. 继承BusinessBase，添加Meta类")
    print("   3. 实现业务方法，使用类型注解")
    print("   4. 重启服务")
    print("   5. AI立即可以使用新方法！")
    
    return True

if __name__ == "__main__":
    test_complete_system()
