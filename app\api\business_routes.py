"""
业务路由
========

专门为业务方法创建的路由，用于MCP工具生成
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

# 创建业务路由器
business_router = APIRouter(prefix="/business", tags=["📁 业务工具"])

# 手动创建一些核心业务路由作为示例
@business_router.post("/创建证书", summary="📜 创建证书", description="创建新的数字证书")
async def create_certificate(
    证书名称: str,
    手机号: str,
    证件号: str,
    算法: str = "SM2",
    证书时长: str = "ONEYEAR",
    配置ID: str = "7876611670"
):
    """创建新的数字证书"""
    try:
        from app.business_classes.certificate_business import CertificateBusiness
        cert_business = CertificateBusiness()
        result = cert_business.创建证书(证书名称, 手机号, 证件号, 算法, 证书时长, 配置ID)
        return result
    except Exception as e:
        logger.error(f"创建证书失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建证书失败: {str(e)}")

@business_router.post("/查询证书详情", summary="📜 查询证书详情", description="查询证书详细信息")
async def get_certificate_detail(证书ID: str, 配置ID: str = "7876611670"):
    """查询证书详细信息"""
    try:
        from app.business_classes.certificate_business import CertificateBusiness
        cert_business = CertificateBusiness()
        result = cert_business.查询证书详情(证书ID, 配置ID)
        return result
    except Exception as e:
        logger.error(f"查询证书详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询证书详情失败: {str(e)}")

@business_router.post("/更新证书", summary="📜 更新证书", description="更新证书信息")
async def update_certificate(
    证书ID: str,
    证书名称: str,
    手机号: str,
    证件号: str,
    算法: str = "SM2",
    证书时长: str = "ONEYEAR",
    配置ID: str = "7876611670"
):
    """更新证书信息"""
    try:
        from app.business_classes.certificate_business import CertificateBusiness
        cert_business = CertificateBusiness()
        result = cert_business.更新证书(证书ID, 证书名称, 手机号, 证件号, 算法, 证书时长, 配置ID)
        return result
    except Exception as e:
        logger.error(f"更新证书失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新证书失败: {str(e)}")

@business_router.post("/吊销证书", summary="📜 吊销证书", description="吊销指定的数字证书")
async def revoke_certificate(证书ID: str):
    """吊销指定的数字证书"""
    try:
        from app.business_classes.certificate_business import CertificateBusiness
        cert_business = CertificateBusiness()
        result = cert_business.吊销证书(证书ID)
        return result
    except Exception as e:
        logger.error(f"吊销证书失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"吊销证书失败: {str(e)}")

@business_router.post("/获取测试账号", summary="👤 获取测试账号", description="获取测试用的账号信息")
async def get_test_account():
    """获取测试用的账号信息"""
    try:
        from app.business_classes.certificate_business import CertificateBusiness
        cert_business = CertificateBusiness()
        result = cert_business.获取测试账号()
        return result
    except Exception as e:
        logger.error(f"获取测试账号失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取测试账号失败: {str(e)}")

@business_router.post("/批量创建证书", summary="📜 批量创建证书", description="批量创建多个证书")
async def batch_create_certificates(数量: int = 3):
    """批量创建多个证书（使用随机测试账号）"""
    try:
        from app.business_classes.certificate_business import CertificateBusiness
        cert_business = CertificateBusiness()
        result = cert_business.批量创建证书(数量)
        return result
    except Exception as e:
        logger.error(f"批量创建证书失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量创建证书失败: {str(e)}")

@business_router.post("/证书完整流程测试", summary="📜 证书完整流程测试", description="执行完整的证书CRUD流程测试")
async def certificate_full_test():
    """执行完整的证书CRUD流程测试"""
    try:
        from app.business_classes.certificate_business import CertificateBusiness
        cert_business = CertificateBusiness()
        result = cert_business.证书完整流程测试()
        return result
    except Exception as e:
        logger.error(f"证书流程测试失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"证书流程测试失败: {str(e)}")

@business_router.post("/生成模拟用户", summary="👤 生成模拟用户", description="生成模拟用户数据")
async def generate_mock_user(数量: int = 1):
    """生成模拟用户数据"""
    try:
        from app.business_classes.user_business import UserBusiness
        user_business = UserBusiness()
        result = user_business.生成模拟用户(数量)
        return result
    except Exception as e:
        logger.error(f"生成模拟用户失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成模拟用户失败: {str(e)}")

@business_router.post("/生成完整用户档案", summary="👤 生成完整用户档案", description="生成包含详细信息的用户档案")
async def generate_user_profile(包含企业信息: bool = False):
    """生成包含详细信息的用户档案"""
    try:
        from app.business_classes.data_generator import DataGenerator
        data_gen = DataGenerator()
        result = data_gen.生成完整用户档案(包含企业信息)
        return result
    except Exception as e:
        logger.error(f"生成用户档案失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成用户档案失败: {str(e)}")
