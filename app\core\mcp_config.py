"""
MCP业务服务配置
==============

专门配置对外暴露的业务MCP工具，排除测试和内部方法
"""
from fastapi import FastAPI
from fastapi_mcp import FastApiMCP
from typing import List, Dict, Any
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# 🚫 排除列表 - 不对外暴露的方法/路径
EXCLUDE_METHODS = [
    # API测试方法
    "ai_chat_api_ai_chat_post",
    "simple_ai_chat_api_ai_simple_chat_post", 
    "get_available_tools_api_ai_tools_get",
    "test_mcp_tool_api_ai_test_tool__tool_name__post",
    "ai_health_check_api_ai_health_get",
    "get_mcp_tools_api_ai_mcp_tools_get",
    "call_mcp_tool_api_ai_mcp_call_post",
    "simple_mcp_chat_api_ai_simple_mcp_chat_post",
    "get_simple_mcp_info_api_ai_simple_mcp_info_get",
    "simple_mcp_demo_api_ai_simple_mcp_demo_post",
    
    # 用户API方法
    "get_user_or_enterprise_info",
    "generate_mock_user_api_users_mock_user_post",
    
    # 内部测试方法
    "test_",
    "demo_",
    "_internal",
    "_test",
    
    # 其他不需要暴露的方法
    "health",
    "ping",
    "status"
]

# 🚫 排除路径模式
EXCLUDE_PATH_PATTERNS = [
    "/api/ai/",
    "/api/users/",
    "/docs",
    "/redoc",
    "/openapi.json"
]

def should_exclude_method(method_name: str, method_path: str = "") -> bool:
    """
    判断方法是否应该被排除
    
    :param method_name: 方法名称
    :param method_path: 方法路径
    :return: 是否排除
    """
    # 检查方法名是否在排除列表中
    for exclude_pattern in EXCLUDE_METHODS:
        if exclude_pattern in method_name.lower():
            return True
    
    # 检查路径是否匹配排除模式
    for exclude_pattern in EXCLUDE_PATH_PATTERNS:
        if exclude_pattern in method_path:
            return True
    
    # 排除私有方法
    if method_name.startswith('_'):
        return True
        
    return False

def create_business_mcp_server(app: FastAPI) -> FastApiMCP:
    """
    创建只包含业务方法的MCP服务器

    :param app: FastAPI应用实例
    :return: 配置好的MCP服务器
    """
    logger.info("🚀 创建业务MCP服务器...")

    # 创建MCP服务器，使用过滤机制
    mcp = FastApiMCP(
        app,
        name=f"{settings.MCP_NAME} - 业务服务",
        description=get_business_mcp_description(),
        describe_all_responses=True,
        describe_full_response_schema=True
    )

    # 应用路由过滤
    apply_route_filters(mcp)

    return mcp

def get_business_mcp_description() -> str:
    """
    获取业务MCP服务器描述

    :return: MCP服务器描述
    """
    return """🚀 专业的业务工具集合

📁 业务模块：
  📜 证书管理: 提供完整的证书增删改查功能
  👤 用户管理: 用户信息生成和验证
  🎲 数据生成: 各种测试数据生成工具
  🛠️ 自定义工具: 实用工具集合

✨ 特性：
  🔥 纯业务方法 - 只包含核心业务功能
  🛡️ 自动过滤 - 排除测试和内部方法
  📝 智能提示 - 详细的方法描述
  🌐 统一接口 - 标准化返回格式"""

def apply_route_filters(mcp: FastApiMCP):
    """
    应用路由过滤，移除不需要的路由

    :param mcp: MCP服务器实例
    """
    try:
        logger.info("🔧 应用路由过滤...")

        # 这里可以添加路由过滤逻辑
        # 由于 FastApiMCP 会自动从 FastAPI 路由生成工具
        # 我们需要确保只有业务路由被包含

        logger.info("✅ 路由过滤完成")

    except Exception as e:
        logger.error(f"❌ 应用路由过滤失败: {str(e)}")


