"""
测试MCP工具列表
==============

测试MCP服务器是否正确暴露业务工具
"""
import requests
import json

def test_mcp_tools():
    """测试MCP工具列表"""
    try:
        # 获取MCP工具列表
        response = requests.get("http://127.0.0.1:8000/mcp")
        
        if response.status_code == 200:
            data = response.json()
            print("🎉 MCP服务器响应成功!")
            print(f"📊 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查是否有工具列表
            if 'tools' in data:
                tools = data['tools']
                print(f"\n🔧 发现 {len(tools)} 个MCP工具:")
                
                business_tools = []
                other_tools = []
                
                for tool in tools:
                    tool_name = tool.get('name', 'Unknown')
                    tool_desc = tool.get('description', 'No description')
                    
                    # 检查是否是业务工具
                    if any(keyword in tool_name.lower() for keyword in ['证书', '用户', '生成', 'business']):
                        business_tools.append((tool_name, tool_desc))
                    else:
                        other_tools.append((tool_name, tool_desc))
                
                print(f"\n📁 业务工具 ({len(business_tools)} 个):")
                for name, desc in business_tools:
                    print(f"  ✅ {name}: {desc[:100]}...")
                
                print(f"\n🔧 其他工具 ({len(other_tools)} 个):")
                for name, desc in other_tools:
                    print(f"  ⚙️ {name}: {desc[:100]}...")
                
                # 分析结果
                if len(business_tools) > 0:
                    print(f"\n🎯 成功! 发现 {len(business_tools)} 个业务工具")
                    if len(other_tools) == 0:
                        print("🔥 完美! 只暴露了业务工具，没有其他无关工具")
                    else:
                        print(f"⚠️ 注意: 还有 {len(other_tools)} 个非业务工具被暴露")
                else:
                    print("❌ 警告: 没有发现业务工具!")
                    
            else:
                print("❌ 响应中没有找到工具列表")
                
        else:
            print(f"❌ MCP服务器响应失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试MCP工具列表...")
    test_mcp_tools()
