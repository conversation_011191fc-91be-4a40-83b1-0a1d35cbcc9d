"""
自定义工具业务类
===============

只需要定义这个类，系统自动生成MCP工具！
"""
import random
import json
from typing import Dict, Any
from datetime import datetime
from app.business_classes import BusinessBase


class CustomTools(BusinessBase):
    """自定义工具业务类"""
    
    class Meta:
        name = "实用工具"
        icon = "⚙️"
        description = "提供各种实用功能，包括密码生成、JSON格式化、文本分析、时间计算等"
        category = "utility"
        ai_prompt_hint = "当用户需要实用工具时，如生成密码、格式化数据、分析文本、计算时间差等，请使用此类的方法"
    
    def 生成测试密码(self, 数量: int = 1, 长度: int = 8, 包含特殊字符: bool = True) -> Dict[str, Any]:
        """
        生成测试用密码
        
        :param 数量: 生成密码的数量，默认1个
        :param 长度: 密码长度，默认8位
        :param 包含特殊字符: 是否包含特殊字符，默认True
        :return: 生成的密码列表
        """
        try:
            import string
            
            # 字符集
            letters = string.ascii_letters  # a-z, A-Z
            digits = string.digits  # 0-9
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?" if 包含特殊字符 else ""
            
            all_chars = letters + digits + special_chars
            
            passwords = []
            for _ in range(数量):
                password = ''.join(random.choice(all_chars) for _ in range(长度))
                passwords.append(password)
            
            return {
                "status": "success",
                "data": {
                    "密码列表": passwords,
                    "数量": len(passwords),
                    "长度": 长度,
                    "包含特殊字符": 包含特殊字符
                },
                "message": f"成功生成{len(passwords)}个密码"
            }
            
        except Exception as e:
            self.logger.error(f"生成密码失败: {str(e)}")
            return {
                "status": "error",
                "message": f"生成密码失败: {str(e)}"
            }
    
    def 格式化JSON数据(self, json_字符串: str) -> Dict[str, Any]:
        """
        格式化JSON数据，使其更易读
        
        :param json_字符串: 需要格式化的JSON字符串
        :return: 格式化后的JSON
        """
        try:
            # 解析JSON
            data = json.loads(json_字符串)
            
            # 格式化输出
            formatted_json = json.dumps(data, ensure_ascii=False, indent=2)
            
            return {
                "status": "success",
                "data": {
                    "原始JSON": json_字符串,
                    "格式化JSON": formatted_json,
                    "数据类型": type(data).__name__
                },
                "message": "JSON格式化成功"
            }
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON格式错误: {str(e)}")
            return {
                "status": "error",
                "message": f"JSON格式错误: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"格式化JSON失败: {str(e)}")
            return {
                "status": "error",
                "message": f"格式化JSON失败: {str(e)}"
            }
    
    def 计算时间差(self, 开始时间: str, 结束时间: str, 时间格式: str = "%Y-%m-%d %H:%M:%S") -> Dict[str, Any]:
        """
        计算两个时间之间的差值
        
        :param 开始时间: 开始时间字符串
        :param 结束时间: 结束时间字符串
        :param 时间格式: 时间格式，默认 "%Y-%m-%d %H:%M:%S"
        :return: 时间差计算结果
        """
        try:
            # 解析时间
            start_dt = datetime.strptime(开始时间, 时间格式)
            end_dt = datetime.strptime(结束时间, 时间格式)
            
            # 计算差值
            time_diff = end_dt - start_dt
            
            # 提取各种时间单位
            total_seconds = int(time_diff.total_seconds())
            days = time_diff.days
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            
            return {
                "status": "success",
                "data": {
                    "开始时间": 开始时间,
                    "结束时间": 结束时间,
                    "总秒数": total_seconds,
                    "天数": days,
                    "小时数": hours,
                    "分钟数": minutes,
                    "秒数": seconds,
                    "时间差描述": f"{days}天{hours}小时{minutes}分钟{seconds}秒"
                },
                "message": "时间差计算成功"
            }
            
        except ValueError as e:
            self.logger.error(f"时间格式错误: {str(e)}")
            return {
                "status": "error",
                "message": f"时间格式错误: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"计算时间差失败: {str(e)}")
            return {
                "status": "error",
                "message": f"计算时间差失败: {str(e)}"
            }
    
    def 文本统计分析(self, 文本内容: str) -> Dict[str, Any]:
        """
        分析文本内容的统计信息
        
        :param 文本内容: 需要分析的文本
        :return: 文本统计结果
        """
        try:
            import re
            
            # 基本统计
            字符总数 = len(文本内容)
            字符数_不含空格 = len(文本内容.replace(' ', ''))
            行数 = len(文本内容.split('\n'))
            段落数 = len([p for p in 文本内容.split('\n\n') if p.strip()])
            
            # 词语统计
            words = re.findall(r'\b\w+\b', 文本内容.lower())
            单词总数 = len(words)
            唯一单词数 = len(set(words))
            
            # 中文字符统计
            中文字符 = re.findall(r'[\u4e00-\u9fff]', 文本内容)
            中文字符数 = len(中文字符)
            
            # 数字统计
            数字 = re.findall(r'\d', 文本内容)
            数字个数 = len(数字)
            
            # 标点符号统计
            标点符号 = re.findall(r'[^\w\s\u4e00-\u9fff]', 文本内容)
            标点符号数 = len(标点符号)
            
            return {
                "status": "success",
                "data": {
                    "字符总数": 字符总数,
                    "字符数_不含空格": 字符数_不含空格,
                    "行数": 行数,
                    "段落数": 段落数,
                    "单词总数": 单词总数,
                    "唯一单词数": 唯一单词数,
                    "中文字符数": 中文字符数,
                    "数字个数": 数字个数,
                    "标点符号数": 标点符号数,
                    "平均每行字符数": round(字符总数 / 行数, 2) if 行数 > 0 else 0
                },
                "message": "文本统计分析完成"
            }
            
        except Exception as e:
            self.logger.error(f"文本统计分析失败: {str(e)}")
            return {
                "status": "error",
                "message": f"文本统计分析失败: {str(e)}"
            }
