# 🚀 AI MCP 服务使用指南

## 📋 功能概述

本系统提供了完整的AI智能助手解决方案，支持：
- 🤖 多轮对话交互
- 🔧 智能工具调用
- 📜 证书管理功能
- 👤 用户信息处理
- 🎲 测试数据生成
- ⚙️ 实用工具集合

## 🌐 访问方式

### 1. 前端界面访问

#### 简单聊天界面
```
http://localhost:8000/api/ai/chat-ui
```
- 基础对话功能
- 简洁的用户界面
- 支持工具调用

#### 高级多轮对话界面
```
http://localhost:8000/api/ai/advanced-chat-ui
```
- 完整的多轮对话体验
- 对话历史管理
- 对话导出功能
- 个性化设置

### 2. API接口调用

#### 多轮对话接口
```bash
curl -X POST http://localhost:8000/api/ai/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "帮我获取一个测试用户信息",
    "conversation_history": [
      {"role": "user", "content": "你好"},
      {"role": "assistant", "content": "你好！我是AI助手"}
    ],
    "use_tools": true
  }'
```

#### 简单对话接口
```bash
curl -X POST http://localhost:8000/api/ai/simple-chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好，请介绍一下你的功能"
  }'
```

### 3. MCP协议接入

#### 获取可用工具
```bash
curl http://localhost:8000/api/ai/mcp/tools
```

#### 调用MCP工具
```bash
curl -X POST http://localhost:8000/api/ai/mcp/call \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "获取用户信息",
    "parameters": {"环境": 1}
  }'
```

## 🔧 核心功能使用

### 1. 证书管理功能

#### 创建证书申请任务
```
用户: "帮我创建一个个人证书申请任务"
AI: 自动调用证书创建工具，返回任务ID
```

#### 查询证书任务状态
```
用户: "查询任务cert_task_xxx的状态"
AI: 自动调用状态查询工具，返回详细进度
```

#### 批量证书操作
```
用户: "批量创建10个个人证书任务"
AI: 自动循环调用创建工具，返回所有任务ID
```

### 2. 用户信息管理

#### 获取测试用户
```
用户: "给我一个测试环境的用户信息"
AI: 返回完整的测试用户数据
```

#### 生成模拟用户
```
用户: "生成一个企业用户信息"
AI: 创建包含完整信息的企业用户档案
```

### 3. 数据生成工具

#### 生成测试数据
```
用户: "生成10个手机号码"
AI: 返回10个有效的手机号码列表
```

#### 生成用户档案
```
用户: "生成一个完整的用户档案"
AI: 返回包含姓名、身份证、手机、邮箱等的完整档案
```

### 4. 实用工具功能

#### 密码生成
```
用户: "生成一个16位的强密码"
AI: 返回符合要求的安全密码
```

#### JSON格式化
```
用户: "帮我格式化这个JSON: {\"name\":\"test\"}"
AI: 返回格式化后的JSON字符串
```

## 🛠️ 高级功能

### 1. 对话历史管理

#### 保存对话
- 系统自动保存对话历史
- 支持多个对话会话
- 本地浏览器存储

#### 导出对话
- 支持Markdown格式导出
- 包含完整的对话记录
- 包含工具调用详情

### 2. 个性化设置

#### 工具调用开关
```javascript
// 在高级界面中可以开启/关闭工具调用
toggleTools() // 切换工具调用状态
```

#### 自动保存设置
```javascript
// 控制是否自动保存对话历史
toggleAutoSave() // 切换自动保存状态
```

### 3. 健康监控

#### 检查服务状态
```bash
curl http://localhost:8000/api/ai/mcp/health
```

#### 强制刷新连接
```bash
curl -X POST http://localhost:8000/api/ai/mcp/refresh
```

#### 获取详细状态
```bash
curl http://localhost:8000/api/ai/mcp/status
```

## 🔧 故障排除

### 1. MCP服务重启问题

**问题**: MCP服务重启后，需要手动刷新才能正常使用

**解决方案**:
```bash
# 方法1: 使用强制刷新接口
curl -X POST http://localhost:8000/api/ai/mcp/refresh

# 方法2: 等待自动恢复（30秒内）
# 系统会自动检测并重连

# 方法3: 在前端界面刷新页面
```

### 2. 工具调用失败

**问题**: AI无法调用工具或调用失败

**排查步骤**:
1. 检查MCP服务健康状态
2. 确认工具列表是否正常
3. 查看错误日志
4. 尝试强制刷新连接

### 3. 对话响应慢

**问题**: AI响应时间过长

**优化建议**:
1. 检查网络连接
2. 确认AI模型服务状态
3. 减少对话历史长度
4. 关闭不必要的工具调用

## 📊 性能优化

### 1. 缓存机制
- 工具列表缓存5分钟
- 对话历史本地存储
- 健康状态实时更新

### 2. 连接管理
- 自动重连机制
- 连接池复用
- 超时重试策略

### 3. 前端优化
- 响应式设计
- 异步加载
- 本地存储管理

## 🔒 安全注意事项

### 1. API访问控制
- 当前版本无认证机制
- 建议在内网环境使用
- 生产环境需要添加认证

### 2. 数据安全
- 对话历史本地存储
- 敏感信息请谨慎处理
- 定期清理缓存数据

### 3. 网络安全
- 使用HTTPS传输
- 配置防火墙规则
- 监控异常访问

## 📞 技术支持

### 1. 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看MCP服务日志
tail -f logs/mcp.log
```

### 2. 配置检查
```bash
# 检查配置文件
cat app/core/config.py

# 检查环境变量
env | grep DEEPSEEK
```

### 3. 服务状态
```bash
# 检查服务进程
ps aux | grep python

# 检查端口占用
netstat -tlnp | grep 8000
```

## 🚀 最佳实践

### 1. 对话技巧
- 使用清晰的指令
- 提供必要的上下文
- 合理利用对话历史

### 2. 工具使用
- 了解可用工具功能
- 提供准确的参数
- 检查调用结果

### 3. 系统维护
- 定期检查健康状态
- 及时处理异常告警
- 保持系统更新

---

*如有问题，请查看日志文件或联系技术支持。*
